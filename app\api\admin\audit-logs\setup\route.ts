import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { readFileSync } from "fs"
import { join } from "path"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authHeader = request.headers.get('authorization')

    if (authHeader) {
      const token = authHeader.replace('Bearer ', '')

      try {
        // Verify the token
        const { data: { user }, error } = await adminClient.auth.getUser(token);

        if (error || !user) {
          return NextResponse.json(
            { error: "Invalid authentication token" },
            { status: 401 }
          )
        }

        // Check if the user has admin permissions
        const { data: userProfile, error: profileError } = await adminClient
          .from("users")
          .select("role")
          .eq("email", user.email)
          .single()

        if (profileError || !userProfile) {
          return NextResponse.json(
            { error: "User profile not found" },
            { status: 403 }
          )
        }

        // Check if the user has admin or super_admin role
        if (userProfile.role !== 'admin' && userProfile.role !== 'super_admin') {
          return NextResponse.json(
            { error: "You do not have permission to set up audit logs" },
            { status: 403 }
          )
        }

        console.log("Admin access verified for audit logs setup:", user.email, "with role:", userProfile.role)
      } catch (authError) {
        console.error("Error verifying token:", authError)
        return NextResponse.json(
          { error: "Authentication error" },
          { status: 401 }
        )
      }
    } else {
      return NextResponse.json(
        { error: "No authentication token provided" },
        { status: 401 }
      )
    }

    console.log("Setting up audit logs system...")

    // Check if audit_logs table already exists
    const { data: tableExists, error: tableCheckError } = await adminClient
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'audit_logs')
      .single()

    if (tableExists && !tableCheckError) {
      return NextResponse.json({
        success: true,
        message: "Audit logs system is already set up",
        tableExists: true
      })
    }

    // Read the migration file
    const migrationPath = join(process.cwd(), 'database', 'migrations', 'create_audit_logs_table.sql')
    let migrationSQL: string

    try {
      migrationSQL = readFileSync(migrationPath, 'utf8')
    } catch (fileError) {
      console.error('Error reading migration file:', fileError)
      return NextResponse.json({
        error: "Migration file not found. Please ensure the audit logs migration file exists.",
        details: "Expected file: database/migrations/create_audit_logs_table.sql"
      }, { status: 500 })
    }

    // Execute the migration SQL
    const { data, error } = await adminClient.rpc('exec_sql', {
      sql: migrationSQL
    })

    if (error) {
      console.error('Error executing audit logs migration:', error)

      // Try alternative approach using direct SQL execution
      try {
        const { error: directError } = await adminClient
          .from('audit_logs')
          .select('id')
          .limit(1)

        if (directError && directError.message?.includes('relation "audit_logs" does not exist')) {
          return NextResponse.json({
            error: "Failed to create audit logs table. Please run the migration manually.",
            details: "Execute the SQL in database/migrations/create_audit_logs_table.sql in your Supabase SQL editor.",
            sqlError: error.message
          }, { status: 500 })
        }
      } catch (testError) {
        console.error('Error testing audit logs table:', testError)
      }

      return NextResponse.json({
        error: "Failed to set up audit logs system",
        details: error.message
      }, { status: 500 })
    }

    // Verify the table was created
    const { data: verifyTable, error: verifyError } = await adminClient
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'audit_logs')
      .single()

    if (verifyError || !verifyTable) {
      return NextResponse.json({
        error: "Audit logs table creation could not be verified",
        details: "The migration may have run but table verification failed"
      }, { status: 500 })
    }

    console.log("Audit logs system set up successfully")

    return NextResponse.json({
      success: true,
      message: "Audit logs system has been set up successfully",
      tableCreated: true,
      data: data
    })

  } catch (error) {
    console.error('Error in audit logs setup API:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
