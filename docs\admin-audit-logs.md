# Admin Audit Logs System

The audit logs system provides comprehensive tracking of all database changes across the Loop Jersey platform. This is essential for compliance, debugging, and security monitoring.

## Features

- **Complete Database Audit Trail**: Tracks INSERT, UPDATE, and DELETE operations
- **User Attribution**: Links changes to the user who made them
- **Data Change Tracking**: Stores both old and new values for all changes
- **Filtering and Search**: Filter by table, action, record ID, or search terms
- **Pagination**: Handles large volumes of audit data efficiently
- **Real-time Updates**: New audit entries appear immediately

## Accessing Audit Logs

1. Navigate to **Admin Panel** → **System** → **Audit Logs**
2. The page is located at `/admin/audit-logs`
3. Only users with `admin` or `super_admin` roles can access audit logs

## Setting Up Audit Logs

If this is your first time accessing audit logs, you'll see a setup message. Click the **"Set Up Audit Logs"** button to automatically:

1. Create the `audit_logs` table
2. Set up audit trigger functions
3. Apply triggers to important tables
4. Configure proper permissions

### Manual Setup

If automatic setup fails, you can manually run the migration:

1. Go to your Supabase SQL Editor
2. Execute the SQL from `database/migrations/create_audit_logs_table.sql`

## Monitored Tables

The following tables are automatically monitored for changes:

- `businesses` - Business registrations and updates
- `users` - User account changes
- `orders` - Order creation and status changes
- `driver_profiles` - Driver applications and updates
- `products` - Product catalog changes
- `business_requests` - Business registration requests
- `feature_requests` - Feature requests from users
- `service_requests` - Service requests from users
- `admin_notifications` - Admin notification changes

## Using the Audit Logs Interface

### Filters

- **Table**: Filter by specific database table
- **Action**: Filter by INSERT, UPDATE, or DELETE operations
- **Record ID**: Search for changes to a specific record
- **Search**: Free-text search across table names, record IDs, and user information

### Audit Log Information

Each audit log entry shows:

- **Timestamp**: When the change occurred (in Jersey timezone)
- **Table**: Which database table was affected
- **Action**: Type of operation (INSERT/UPDATE/DELETE)
- **Record ID**: The ID of the affected record
- **User**: Who made the change (or "System" for automated changes)
- **Changes**: Summary of what was modified

### Data Changes

- **INSERT**: Shows "Created" with field count
- **UPDATE**: Shows "Modified" with changed field count
- **DELETE**: Shows "Deleted" indicating record removal

## Security and Permissions

- **Row Level Security**: Only admins can view audit logs
- **User Attribution**: Changes are linked to authenticated users
- **System Changes**: Automated changes are marked as "System"
- **Data Privacy**: Sensitive data is stored securely in JSONB format

## Performance Considerations

- **Indexes**: Optimized for common query patterns
- **Pagination**: Handles large datasets efficiently
- **Cleanup**: Consider implementing data retention policies for long-term use

## Troubleshooting

### Audit Logs Not Appearing

1. Verify the audit triggers are installed on the table
2. Check that the user making changes is authenticated
3. Ensure RLS policies allow the admin user to view logs

### Performance Issues

1. Use specific filters rather than viewing all logs
2. Consider the time range of your queries
3. Check database indexes are properly created

### Missing Tables

To add audit logging to a new table:

```sql
SELECT public.add_audit_triggers('your_table_name');
```

To remove audit logging from a table:

```sql
SELECT public.remove_audit_triggers('your_table_name');
```

## API Endpoints

- `GET /api/admin/audit-logs` - Fetch audit logs with filtering
- `POST /api/admin/audit-logs/setup` - Set up the audit logs system

## Database Schema

The `audit_logs` table structure:

```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    record_id VARCHAR(100) NOT NULL,
    action VARCHAR(10) CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
    old_values JSONB,
    new_values JSONB,
    user_id UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    ip_address INET,
    user_agent TEXT,
    session_id TEXT
);
```

## Best Practices

1. **Regular Monitoring**: Check audit logs regularly for unusual activity
2. **Filter Usage**: Use filters to focus on relevant changes
3. **Data Retention**: Implement cleanup policies for old audit data
4. **Security**: Ensure only authorized admins have access
5. **Performance**: Monitor query performance with large datasets

## Related Features

- **Admin Notifications**: Real-time alerts for important changes
- **Trigger Performance Logs**: Monitor database trigger performance
- **User Activity Tracking**: Track user actions across the platform
