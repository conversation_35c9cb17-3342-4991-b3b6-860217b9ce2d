"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  MessageSquare, 
  Package, 
  Utensils, 
  Truck,
  Clock,
  Star,
  ArrowRight,
  Zap,
  AlertCircle
} from "lucide-react"
import { cn } from "@/lib/utils"
import { MESSAGE_TEMPLATES, QUICK_RESPONSES, MessageTemplate, QuickResponse } from '@/types/message-templates'

interface User {
  id: string
  email?: string
  user_metadata?: any
}

interface Context {
  order_id?: string | null
  business_id?: string | null
  rider_id?: string | null
  role?: 'customer' | 'business' | 'rider' | null
}

interface EnhancedQuickAction {
  id: string
  type: 'template' | 'quick_response' | 'custom'
  title: string
  description?: string
  content?: string
  icon: React.ReactNode
  priority: number
  is_urgent?: boolean
  template?: MessageTemplate
  quick_response?: QuickResponse
  context?: any
}

interface EnhancedQuickActionsProps {
  user: User
  context?: Context
  onTemplateSelected: (template: MessageTemplate, context?: any) => void
  onQuickResponseSelected: (response: QuickResponse) => void
  onCustomActionSelected: (action: any) => void
}

export function EnhancedQuickActions({ 
  user, 
  context, 
  onTemplateSelected, 
  onQuickResponseSelected,
  onCustomActionSelected 
}: EnhancedQuickActionsProps) {
  const [actions, setActions] = useState<EnhancedQuickAction[]>([])
  const [quickResponses, setQuickResponses] = useState<QuickResponse[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [userRole, setUserRole] = useState<'customer' | 'business' | 'driver'>('customer')

  useEffect(() => {
    generateContextualActions()
  }, [user, context])

  const generateContextualActions = async () => {
    setIsLoading(true)

    try {
      // Get contextual data from API
      const contextData = await fetchContextualData(user.id, context)
      const role = contextData.user_role || 'customer'
      setUserRole(role)

      // Get contextual templates based on user role and context
      const contextualTemplates = getContextualTemplates(role, context, contextData)

      // Get relevant quick responses
      const relevantQuickResponses = QUICK_RESPONSES.filter(qr =>
        qr.user_roles.includes(role)
      )
      setQuickResponses(relevantQuickResponses)

      // Convert templates to actions
      const templateActions: EnhancedQuickAction[] = contextualTemplates.map(template => ({
        id: template.id,
        type: 'template',
        title: template.title,
        description: getTemplateDescription(template, context, contextData),
        content: template.content,
        icon: getTemplateIcon(template),
        priority: template.priority,
        is_urgent: template.is_urgent,
        template
      }))

      // Add custom contextual actions based on real data
      const customActions = await getCustomActions(user, context, role, contextData)

      // Combine and sort by priority
      const allActions = [...templateActions, ...customActions]
        .sort((a, b) => a.priority - b.priority)
        .slice(0, 6) // Limit to top 6 actions

      setActions(allActions)
    } catch (error) {
      console.error('Error generating contextual actions:', error)
      // Fallback to basic actions
      setActions(getDefaultActions(userRole))
    } finally {
      setIsLoading(false)
    }
  }

  const getContextualTemplates = (role: 'customer' | 'business' | 'driver', context?: Context, contextData?: any): MessageTemplate[] => {
    return MESSAGE_TEMPLATES.filter(template => {
      // Check if user role can use this template
      if (!template.user_roles.includes(role)) return false

      // Special logic for review templates - show them if user has recent completed orders or business interactions
      if (template.id === 'order-review' && role === 'customer') {
        return contextData?.has_recent_completed_orders
      }
      if (template.id === 'business-review' && role === 'customer') {
        return contextData?.has_recent_business_interactions
      }
      if (template.id === 'quick-positive-review' && role === 'customer') {
        return contextData?.has_recent_completed_orders || contextData?.has_recent_business_interactions
      }

      // Check context requirements for other templates
      if (template.context_required) {
        if (template.context_required.order_id && !context?.order_id && !contextData?.has_active_orders) return false
        if (template.context_required.business_id && !context?.business_id && !contextData?.has_recent_business_interactions) return false
        if (template.context_required.driver_id && !context?.rider_id) return false
      }

      // Prioritize templates based on current context
      if (contextData?.has_active_orders && template.category === 'order') {
        template.priority = Math.max(1, template.priority - 1) // Boost priority
      }

      return true
    }).sort((a, b) => a.priority - b.priority)
  }

  const getTemplateIcon = (template: MessageTemplate): React.ReactNode => {
    switch (template.category) {
      case 'order':
        return <Package className="h-5 w-5 text-orange-600" />
      case 'delivery':
        return <Truck className="h-5 w-5 text-blue-600" />
      case 'business':
        return <Utensils className="h-5 w-5 text-green-600" />
      case 'customer_service':
        return <AlertCircle className="h-5 w-5 text-red-600" />
      default:
        return <MessageSquare className="h-5 w-5 text-emerald-600" />
    }
  }

  const getTemplateDescription = (template: MessageTemplate, context?: Context, contextData?: any): string => {
    // Special descriptions for review templates
    if (template.id === 'order-review' && contextData?.recent_completed_orders?.[0]) {
      const order = contextData.recent_completed_orders[0]
      const businessName = order.businesses?.display_name || order.businesses?.name
      return `Review order #${order.order_number} from ${businessName}`
    }

    if (template.id === 'business-review' && contextData?.recent_business_interactions?.[0]) {
      const interaction = contextData.recent_business_interactions[0]
      const businessName = interaction.businesses?.display_name || interaction.businesses?.name
      return `Share your experience with ${businessName}`
    }

    if (template.id === 'quick-positive-review') {
      if (contextData?.recent_completed_orders?.[0]) {
        const order = contextData.recent_completed_orders[0]
        const businessName = order.businesses?.display_name || order.businesses?.name
        return `Quick thank you to ${businessName}`
      }
      return 'Send a quick positive review'
    }

    if (template.variables && (context || contextData)) {
      // Try to populate some variables for preview
      let description = template.content

      // Use context data for more accurate previews
      if (contextData?.order?.order_number && template.variables.includes('order_number')) {
        description = description.replace('{order_number}', `#${contextData.order.order_number}`)
      } else if (context?.order_id && template.variables.includes('order_number')) {
        description = description.replace('{order_number}', `#${context.order_id.slice(-6)}`)
      }

      if (contextData?.business?.name && template.variables.includes('business_name')) {
        description = description.replace('{business_name}', contextData.business.name)
      }

      return description.length > 60 ? description.substring(0, 60) + '...' : description
    }
    return template.content.length > 60 ? template.content.substring(0, 60) + '...' : template.content
  }

  const getCustomActions = async (user: User, context?: Context, role?: string, contextData?: any): Promise<EnhancedQuickAction[]> => {
    const customActions: EnhancedQuickAction[] = []

    // Check for active orders using real data
    if (role === 'customer' && contextData?.has_active_orders) {
      customActions.push({
        id: 'check-active-orders',
        type: 'custom',
        title: 'Check my active orders',
        description: `You have ${contextData.active_orders?.length || 0} active order(s)`,
        icon: <Clock className="h-5 w-5 text-orange-600" />,
        priority: 1,
        context: { type: 'active_orders', orders: contextData.active_orders }
      })
    }

    // Add role-specific actions
    if (role === 'business') {
      customActions.push({
        id: 'coordinate-delivery',
        type: 'custom',
        title: 'Coordinate with drivers',
        description: 'Manage delivery logistics',
        icon: <Truck className="h-5 w-5 text-blue-600" />,
        priority: 2,
        context: { type: 'driver_coordination' }
      })
    }

    // Add recent conversation continuation if available
    if (contextData?.recent_conversations) {
      customActions.push({
        id: 'continue-recent',
        type: 'custom',
        title: 'Continue recent conversation',
        description: 'Pick up where you left off',
        icon: <MessageSquare className="h-5 w-5 text-purple-600" />,
        priority: 3,
        context: { type: 'recent_conversation' }
      })
    }

    return customActions
  }

  const getDefaultActions = (role: 'customer' | 'business' | 'driver'): EnhancedQuickAction[] => {
    return [
      {
        id: 'general-message',
        type: 'custom',
        title: 'Start a conversation',
        description: 'Connect with someone new',
        icon: <MessageSquare className="h-5 w-5 text-emerald-600" />,
        priority: 1,
        context: { type: 'general' }
      }
    ]
  }

  // API functions for real data
  const fetchContextualData = async (userId: string, context?: Context) => {
    try {
      const params = new URLSearchParams({ user_id: userId })
      if (context?.order_id) params.append('order_id', context.order_id)
      if (context?.business_id) params.append('business_id', context.business_id)
      if (context?.rider_id) params.append('rider_id', context.rider_id)

      const response = await fetch(`/api/messages/context?${params}`)
      const data = await response.json()

      if (data.success) {
        return data.context
      }
      throw new Error(data.error || 'Failed to fetch context')
    } catch (error) {
      console.error('Error fetching contextual data:', error)
      return {
        user_role: 'customer',
        has_active_orders: false,
        recent_conversations: false
      }
    }
  }

  const getUserRole = async (user: User): Promise<'customer' | 'business' | 'driver'> => {
    try {
      const response = await fetch('/api/user/profile', {
        headers: {
          'Authorization': `Bearer ${await user.getIdToken?.()}` // If using Firebase
        }
      })
      const data = await response.json()
      return data.role || 'customer'
    } catch (error) {
      console.error('Error fetching user role:', error)
      return 'customer' // Default fallback
    }
  }

  const checkActiveOrders = async (userId: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/orders/active?user_id=${userId}`)
      const data = await response.json()
      return data.hasActiveOrders || false
    } catch (error) {
      console.error('Error checking active orders:', error)
      return false
    }
  }

  const handleActionClick = (action: EnhancedQuickAction) => {
    if (action.type === 'template' && action.template) {
      onTemplateSelected(action.template, context)
    } else if (action.type === 'quick_response' && action.quick_response) {
      onQuickResponseSelected(action.quick_response)
    } else {
      onCustomActionSelected(action)
    }
  }

  const handleQuickResponseClick = (response: QuickResponse) => {
    onQuickResponseSelected(response)
  }

  if (isLoading) {
    return (
      <div className="px-4 space-y-3">
        <div className="h-4 bg-gray-200 rounded animate-pulse" />
        <div className="grid grid-cols-2 gap-2">
          {[1, 2, 3, 4].map(i => (
            <div key={i} className="h-20 bg-gray-200 rounded animate-pulse" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="px-4 space-y-4">
      {/* Quick Actions */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Zap className="h-4 w-4 text-emerald-600" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid grid-cols-2 gap-2">
            {actions.map((action) => (
              <Button
                key={action.id}
                variant="outline"
                className={cn(
                  "h-auto p-3 flex flex-col items-start text-left space-y-1 hover:bg-gray-50",
                  action.is_urgent && "border-red-200 bg-red-50/30"
                )}
                onClick={() => handleActionClick(action)}
              >
                <div className="flex items-center gap-2 w-full">
                  {action.icon}
                  {action.is_urgent && (
                    <Badge variant="destructive" className="text-xs px-1 py-0">
                      Urgent
                    </Badge>
                  )}
                </div>
                <div className="text-xs font-medium">{action.title}</div>
                {action.description && (
                  <div className="text-xs text-gray-500 line-clamp-2">
                    {action.description}
                  </div>
                )}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Responses */}
      {quickResponses.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Quick Responses</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex flex-wrap gap-2">
              {quickResponses.slice(0, 8).map((response) => (
                <Button
                  key={response.id}
                  variant="outline"
                  size="sm"
                  className="h-8 text-xs"
                  onClick={() => handleQuickResponseClick(response)}
                >
                  {response.emoji && <span className="mr-1">{response.emoji}</span>}
                  {response.text}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
