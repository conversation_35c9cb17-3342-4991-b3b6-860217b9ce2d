"use client"

import { useEffect, useRef } from 'react'

interface AppClosureDetectionOptions {
  onAppClosure?: () => void
  onPageUnload?: () => void
  gracePeriodMs?: number
  enabled?: boolean
}

/**
 * Hook to detect when the driver app/browser is being closed
 * and automatically end their shift with appropriate reason
 */
export function useAppClosureDetection({
  onAppClosure,
  onPageUnload,
  gracePeriodMs = 5000, // 5 second grace period
  enabled = true
}: AppClosureDetectionOptions) {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const isUnloadingRef = useRef(false)

  useEffect(() => {
    if (!enabled) return

    // Handle page visibility changes (app backgrounded/foregrounded)
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // App went to background - start grace period timer
        timeoutRef.current = setTimeout(() => {
          if (document.hidden && !isUnloadingRef.current) {
            // Still hidden after grace period - consider app closed
            onAppClosure?.()
          }
        }, gracePeriodMs)
      } else {
        // App came back to foreground - cancel timer
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current)
          timeoutRef.current = null
        }
      }
    }

    // Handle page unload (browser/tab closing)
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      isUnloadingRef.current = true
      onPageUnload?.()
      
      // Cancel any pending app closure detection
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
    }

    // Handle page unload (alternative event)
    const handleUnload = () => {
      isUnloadingRef.current = true
      onPageUnload?.()
    }

    // Handle page focus/blur (additional detection method)
    const handleBlur = () => {
      // Similar to visibility change but for window focus
      if (!timeoutRef.current) {
        timeoutRef.current = setTimeout(() => {
          if (!document.hasFocus() && !isUnloadingRef.current) {
            onAppClosure?.()
          }
        }, gracePeriodMs)
      }
    }

    const handleFocus = () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
    }

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('unload', handleUnload)
    window.addEventListener('blur', handleBlur)
    window.addEventListener('focus', handleFocus)

    // Cleanup
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('unload', handleUnload)
      window.removeEventListener('blur', handleBlur)
      window.removeEventListener('focus', handleFocus)
      
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [enabled, gracePeriodMs, onAppClosure, onPageUnload])

  // Return function to manually trigger app closure detection
  const triggerAppClosure = () => {
    onAppClosure?.()
  }

  return { triggerAppClosure }
}
