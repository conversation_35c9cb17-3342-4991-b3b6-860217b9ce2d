import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: NextRequest) {
  console.log("=== Audit Logs API Request ===")
  try {
    // Check authentication
    const authHeader = request.headers.get('authorization')
    console.log("Auth header present:", !!authHeader)

    if (authHeader) {
      const token = authHeader.replace('Bearer ', '')

      try {
        // Verify the token
        const { data: { user }, error } = await adminClient.auth.getUser(token);

        if (error || !user) {
          return NextResponse.json(
            { error: "Invalid authentication token" },
            { status: 401 }
          )
        }

        // Check if the user has admin permissions
        const { data: userProfile, error: profileError } = await adminClient
          .from("users")
          .select("role")
          .eq("email", user.email)
          .single()

        if (profileError || !userProfile) {
          return NextResponse.json(
            { error: "User profile not found" },
            { status: 403 }
          )
        }

        // Check if the user has admin or super_admin role
        if (userProfile.role !== 'admin' && userProfile.role !== 'super_admin') {
          return NextResponse.json(
            { error: "You do not have permission to access audit logs" },
            { status: 403 }
          )
        }

        console.log("Admin access verified for audit logs:", user.email, "with role:", userProfile.role)
      } catch (authError) {
        console.error("Error verifying token:", authError)
        return NextResponse.json(
          { error: "Authentication error" },
          { status: 401 }
        )
      }
    } else {
      return NextResponse.json(
        { error: "No authentication token provided" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const table = searchParams.get('table')
    const recordId = searchParams.get('recordId')
    const action = searchParams.get('action')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    console.log("Query parameters:", { table, recordId, action, limit, offset })

    // Try to query audit_logs table directly to check if it exists
    const { error: tableCheckError } = await adminClient
      .from('audit_logs')
      .select('id')
      .limit(1)

    if (tableCheckError) {
      console.log('Audit logs table check error:', tableCheckError)

      if (tableCheckError.message?.includes('relation "audit_logs" does not exist')) {
        console.log('Audit logs table does not exist yet')
        return NextResponse.json({
          logs: [],
          pagination: {
            total: 0,
            limit,
            offset,
            hasMore: false
          },
          message: 'Audit logs table not yet created. Please run the audit logs migration.'
        })
      }

      // If it's not a "table doesn't exist" error, it might be RLS or permissions
      // Let's continue and see what happens with the main query
      console.log('Table exists but there was an error (possibly RLS):', tableCheckError.message)
    }

    // First, let's try without the join to see if the table exists and has data
    let query = adminClient
      .from('audit_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    // Apply filters
    if (table) {
      query = query.eq('table_name', table)
    }
    if (recordId) {
      query = query.eq('record_id', recordId)
    }
    if (action) {
      query = query.eq('action', action)
    }

    console.log("Executing audit logs query...")
    const { data: logs, error } = await query
    console.log("Query result:", { logsCount: logs?.length || 0, error: error?.message })

    if (error) {
      console.error('Error fetching audit logs:', error)
      console.error('Error details:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      })

      // Handle specific error cases
      if (error.message?.includes('relation "audit_logs" does not exist')) {
        return NextResponse.json({
          logs: [],
          pagination: {
            total: 0,
            limit,
            offset,
            hasMore: false
          },
          message: 'Audit logs table not yet created. Please run the audit logs migration.'
        })
      }

      // Handle RLS policy errors
      if (error.message?.includes('RLS') || error.message?.includes('policy') || error.code === 'PGRST116') {
        return NextResponse.json({
          error: 'Access denied. Please ensure you have admin privileges.',
          details: 'Row Level Security policy may be blocking access to audit logs.'
        }, { status: 403 })
      }

      return NextResponse.json({
        error: 'Failed to fetch audit logs',
        details: error.message
      }, { status: 500 })
    }

    // Now fetch user information for the logs that have user_id
    let logsWithUsers = logs || []
    if (logs && logs.length > 0) {
      console.log("Fetching user information for audit logs...")

      // Get unique user IDs from the logs
      const userIds = [...new Set(logs.map(log => log.user_id).filter(Boolean))]
      console.log("User IDs to fetch:", userIds)

      if (userIds.length > 0) {
        try {
          const { data: users, error: usersError } = await adminClient
            .from('users')
            .select('id, name, email')
            .in('id', userIds)

          if (!usersError && users) {
            console.log("Fetched users:", users.length)
            // Create a map of user ID to user data
            const userMap = users.reduce((acc, user) => {
              acc[user.id] = user
              return acc
            }, {} as Record<string, any>)

            // Add user data to logs
            logsWithUsers = logs.map(log => ({
              ...log,
              users: log.user_id ? userMap[log.user_id] : null
            }))
          } else {
            console.error("Error fetching users:", usersError)
            // Continue without user data
          }
        } catch (userFetchError) {
          console.error("Error in user fetch:", userFetchError)
          // Continue without user data
        }
      }
    }

    // Get total count for pagination
    let countQuery = adminClient
      .from('audit_logs')
      .select('*', { count: 'exact', head: true })

    if (table) countQuery = countQuery.eq('table_name', table)
    if (recordId) countQuery = countQuery.eq('record_id', recordId)
    if (action) countQuery = countQuery.eq('action', action)

    const { count, error: countError } = await countQuery

    if (countError) {
      console.error('Error counting audit logs:', countError)
      // Continue with logs but without accurate count
    }

    return NextResponse.json({
      logs: logsWithUsers || [],
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      }
    })

  } catch (error) {
    console.error('Error in audit logs API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
