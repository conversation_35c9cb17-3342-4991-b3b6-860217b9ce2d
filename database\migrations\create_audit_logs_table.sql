-- Create audit logs table for tracking all database changes
-- This provides a comprehensive audit trail for compliance and debugging

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create audit_logs table
CREATE TABLE IF NOT EXISTS public.audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- What was changed
    table_name VARCHAR(100) NOT NULL,
    record_id VARCHAR(100) NOT NULL, -- Using VARCHAR to handle both UUID and integer IDs
    action VARCHAR(10) NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
    
    -- Data changes
    old_values JSONB, -- Previous values (NULL for INSERT)
    new_values JSONB, -- New values (NULL for DELETE)
    
    -- Who made the change
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    
    -- When it happened
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Additional context
    ip_address INET,
    user_agent TEXT,
    session_id TEXT
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_table_name ON public.audit_logs(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_logs_record_id ON public.audit_logs(record_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON public.audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON public.audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON public.audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_table_record ON public.audit_logs(table_name, record_id);

-- Enable RLS
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies - Only admins can view audit logs
CREATE POLICY "Admins can view all audit logs" ON public.audit_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('admin', 'super_admin')
        )
    );

-- Create audit trigger function
CREATE OR REPLACE FUNCTION public.audit_trigger_function() RETURNS TRIGGER AS $$
DECLARE
    old_data JSONB;
    new_data JSONB;
    current_user_id UUID;
BEGIN
    -- Get current user ID from auth context
    current_user_id := auth.uid();
    
    -- Prepare old and new data
    IF TG_OP = 'DELETE' THEN
        old_data := to_jsonb(OLD);
        new_data := NULL;
    ELSIF TG_OP = 'INSERT' THEN
        old_data := NULL;
        new_data := to_jsonb(NEW);
    ELSIF TG_OP = 'UPDATE' THEN
        old_data := to_jsonb(OLD);
        new_data := to_jsonb(NEW);
    END IF;
    
    -- Insert audit log
    INSERT INTO public.audit_logs (
        table_name,
        record_id,
        action,
        old_values,
        new_values,
        user_id,
        created_at
    ) VALUES (
        TG_TABLE_NAME,
        CASE 
            WHEN TG_OP = 'DELETE' THEN OLD.id::TEXT
            ELSE NEW.id::TEXT
        END,
        TG_OP,
        old_data,
        new_data,
        current_user_id,
        now()
    );
    
    -- Return appropriate record
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to add audit triggers to a table
CREATE OR REPLACE FUNCTION public.add_audit_triggers(table_name TEXT) RETURNS VOID AS $$
BEGIN
    -- Drop existing triggers if they exist
    EXECUTE format('DROP TRIGGER IF EXISTS audit_trigger_insert ON %I', table_name);
    EXECUTE format('DROP TRIGGER IF EXISTS audit_trigger_update ON %I', table_name);
    EXECUTE format('DROP TRIGGER IF EXISTS audit_trigger_delete ON %I', table_name);
    
    -- Create new triggers
    EXECUTE format('
        CREATE TRIGGER audit_trigger_insert
        AFTER INSERT ON %I
        FOR EACH ROW
        EXECUTE FUNCTION public.audit_trigger_function()
    ', table_name);
    
    EXECUTE format('
        CREATE TRIGGER audit_trigger_update
        AFTER UPDATE ON %I
        FOR EACH ROW
        EXECUTE FUNCTION public.audit_trigger_function()
    ', table_name);
    
    EXECUTE format('
        CREATE TRIGGER audit_trigger_delete
        AFTER DELETE ON %I
        FOR EACH ROW
        EXECUTE FUNCTION public.audit_trigger_function()
    ', table_name);
    
    RAISE NOTICE 'Audit triggers added to table: %', table_name;
END;
$$ LANGUAGE plpgsql;

-- Function to remove audit triggers from a table
CREATE OR REPLACE FUNCTION public.remove_audit_triggers(table_name TEXT) RETURNS VOID AS $$
BEGIN
    EXECUTE format('DROP TRIGGER IF EXISTS audit_trigger_insert ON %I', table_name);
    EXECUTE format('DROP TRIGGER IF EXISTS audit_trigger_update ON %I', table_name);
    EXECUTE format('DROP TRIGGER IF EXISTS audit_trigger_delete ON %I', table_name);
    
    RAISE NOTICE 'Audit triggers removed from table: %', table_name;
END;
$$ LANGUAGE plpgsql;

-- Add audit triggers to important tables
SELECT public.add_audit_triggers('businesses');
SELECT public.add_audit_triggers('users');
SELECT public.add_audit_triggers('orders');
SELECT public.add_audit_triggers('driver_profiles');
SELECT public.add_audit_triggers('products');
SELECT public.add_audit_triggers('business_requests');
SELECT public.add_audit_triggers('feature_requests');
SELECT public.add_audit_triggers('service_requests');
SELECT public.add_audit_triggers('admin_notifications');

-- Grant permissions
GRANT SELECT ON public.audit_logs TO authenticated;
GRANT EXECUTE ON FUNCTION public.add_audit_triggers(TEXT) TO service_role;
GRANT EXECUTE ON FUNCTION public.remove_audit_triggers(TEXT) TO service_role;

-- Comments
COMMENT ON TABLE public.audit_logs IS 'Comprehensive audit trail for all database changes';
COMMENT ON FUNCTION public.audit_trigger_function() IS 'Trigger function to log all database changes';
COMMENT ON FUNCTION public.add_audit_triggers(TEXT) IS 'Helper function to add audit triggers to a table';
COMMENT ON FUNCTION public.remove_audit_triggers(TEXT) IS 'Helper function to remove audit triggers from a table';

-- Migration complete
SELECT 'Audit logs system created successfully' as status;
