import { useState, useCallback, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/context/unified-auth-context'

interface BusinessData {
  id: number
  name: string
  business_type_id: number
  business_type?: string
  logo_url?: string | null
}

interface BusinessOption {
  id: number
  name: string
  business_type?: string
}

export function useBusinessData() {
  const router = useRouter()
  const { user, isAdmin, isSuperAdmin, userProfile, isLoading: authLoading } = useAuth()

  // State
  const [business, setBusiness] = useState<BusinessData | null>(null)
  const [availableBusinesses, setAvailableBusinesses] = useState<BusinessOption[]>([])
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(null)
  const [isAdminUser, setIsAdminUser] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isPendingApproval, setIsPendingApproval] = useState(false)

  // Check if user is admin or super admin
  useEffect(() => {
    console.log('🔍 Business Data Hook - Admin Check:', {
      isAdmin,
      isSuperAdmin,
      user: !!user,
      userEmail: user?.email,
      userProfile,
      userRole: userProfile?.role,
      authLoading
    })

    // Don't make admin decisions while the auth context is still loading
    if (authLoading) {
      console.log('⏳ Auth context still loading, waiting...')
      return
    }

    // Only proceed if we have a user and the profile has been attempted to load
    if (user && userProfile !== undefined) {
      if (isAdmin || isSuperAdmin) {
        console.log('✅ Setting isAdminUser to true')
        setIsAdminUser(true)
      } else {
        console.log('❌ User is not admin, setting isAdminUser to false')
        setIsAdminUser(false)
      }
    } else if (user && !authLoading) {
      // User exists but no profile loaded and not loading - likely not an admin
      console.log('❌ User exists but no profile loaded, setting isAdminUser to false')
      setIsAdminUser(false)
    }
  }, [isAdmin, isSuperAdmin, user, userProfile, authLoading])

  // Load selected business ID from localStorage
  useEffect(() => {
    if (user && isAdminUser) {
      try {
        const savedBusinessId = localStorage.getItem('loop_jersey_selected_business_id')
        if (savedBusinessId) {
          console.log("Loaded selected business ID from localStorage:", savedBusinessId)
          const businessId = parseInt(savedBusinessId)
          setSelectedBusinessId(businessId)
        }
      } catch (e) {
        console.error("Error loading selected business ID from localStorage:", e)
      }
    }
  }, [user, isAdminUser])

  // Fetch business data
  const fetchBusinessData = useCallback(async () => {
    try {
      console.log("Fetching business data...")
      setIsLoading(true)
      setError(null)

      const token = localStorage.getItem('loop_jersey_auth_token') || ''

      // Build the URL based on whether we're an admin user with a selected business
      let url = '/api/business-admin/business-data'

      if (isAdminUser && selectedBusinessId) {
        url = `/api/business-admin/business-data?businessId=${selectedBusinessId}`
        console.log(`Admin user fetching data for business ID: ${selectedBusinessId}`)
      }

      const response = await fetch(url, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        // If we get a 401 or 403, redirect to login
        if (response.status === 401 || response.status === 403) {
          console.log("Authentication error, redirecting to login")
          router.push("/login?redirectTo=/business-admin/orders")
          return
        }

        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()
      console.log("Business data received:", data)

      if (data.business) {
        setBusiness({
          id: data.business.id,
          name: data.business.name,
          business_type_id: data.business.business_type_id,
          business_type: data.business.business_type
        })

        // Check if the business is pending approval
        setIsPendingApproval(data.business.is_approved === false)
      } else {
        setError("No business data found")
      }
    } catch (err) {
      console.error("Error fetching business data:", err)
      setError("An unexpected error occurred")
    } finally {
      setIsLoading(false)
    }
  }, [isAdminUser, selectedBusinessId, router])

  // Fetch available businesses for admin users
  const fetchAvailableBusinesses = useCallback(async () => {
    if (!isAdminUser) return

    try {
      console.log("Admin user detected, fetching available businesses")

      const token = localStorage.getItem('loop_jersey_auth_token') || ''

      const response = await fetch('/api/admin/businesses-direct', {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()
      console.log("Available businesses:", data)

      if (data && Array.isArray(data)) {
        const businesses = data.map((b: any) => ({
          id: b.id,
          name: b.name,
          business_type: b.business_type || b.business_types?.name || "Business"
        }))

        setAvailableBusinesses(businesses)

        // Auto-select first business if no business is currently selected
        if (!selectedBusinessId && businesses.length > 0) {
          const firstBusiness = businesses[0]
          console.log("🔄 Auto-selecting first business:", firstBusiness.name, "ID:", firstBusiness.id)
          setSelectedBusinessId(firstBusiness.id)

          // Store in localStorage
          try {
            localStorage.setItem('loop_jersey_selected_business_id', firstBusiness.id.toString())
          } catch (e) {
            console.error("Error storing selected business ID:", e)
          }

          // Fetch business data for the auto-selected business
          setTimeout(() => {
            fetchBusinessData()
          }, 100) // Small delay to ensure state is updated
        }
      }
    } catch (err) {
      console.error("Error fetching available businesses:", err)
    }
  }, [isAdminUser, selectedBusinessId])

  // Handle business selection change for admin users
  const handleBusinessChange = useCallback(async (businessId: number) => {
    console.log("Selected business changed to:", businessId)

    // Update the selected business ID
    setSelectedBusinessId(businessId)

    // Store the selected business ID in localStorage for persistence
    try {
      localStorage.setItem('loop_jersey_selected_business_id', businessId.toString())
    } catch (e) {
      console.error("Error storing selected business ID:", e)
    }

    // Refetch data with the new business ID
    await fetchBusinessData()
  }, [fetchBusinessData])

  // Initialize data loading
  useEffect(() => {
    if (user) {
      if (isAdminUser) {
        fetchAvailableBusinesses()
        // For admin users, only fetch business data if a business is selected
        if (selectedBusinessId) {
          fetchBusinessData()
        }
      } else {
        // For non-admin users, fetch business data immediately
        fetchBusinessData()
      }
    }
  }, [user, isAdminUser, selectedBusinessId, fetchAvailableBusinesses, fetchBusinessData])

  return {
    business,
    availableBusinesses,
    selectedBusinessId,
    isAdminUser,
    isLoading,
    error,
    isPendingApproval,
    handleBusinessChange,
    fetchBusinessData,
    fetchAvailableBusinesses
  }
}
