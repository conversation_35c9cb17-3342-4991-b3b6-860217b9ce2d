import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Create a Supabase client with service role key for admin operations
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

// Business type to attribute type mapping
const BUSINESS_TYPE_ATTRIBUTES: Record<number, string[]> = {
  1: ['cuisine', 'restaurant_service_feature'], // Restaurant
  2: ['store_type', 'store_feature', 'delivery_option'], // Shop
  3: ['pharmacy_type', 'pharmacy_service_feature'], // Pharmacy
  4: ['cafe_type', 'cafe_feature'], // Cafe
  38: ['errand_type', 'transport_type'] // Errand
}

// GET - Fetch available attributes for a business type
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const businessTypeId = searchParams.get('businessTypeId')

    if (!businessTypeId) {
      return NextResponse.json(
        { error: 'Business type ID is required' },
        { status: 400 }
      )
    }

    const businessTypeIdNum = parseInt(businessTypeId)
    const allowedAttributeTypes = BUSINESS_TYPE_ATTRIBUTES[businessTypeIdNum]

    if (!allowedAttributeTypes) {
      return NextResponse.json(
        { error: 'Invalid business type' },
        { status: 400 }
      )
    }

    // Get all unique attribute values for the allowed attribute types
    const { data: attributes, error } = await adminClient
      .from('business_attributes')
      .select('attribute_type, attribute_value')
      .in('attribute_type', allowedAttributeTypes)

    if (error) {
      console.error('Error fetching available attributes:', error)
      return NextResponse.json(
        { error: 'Failed to fetch available attributes' },
        { status: 500 }
      )
    }

    // Get unique combinations
    const uniqueAttributes = Array.from(
      new Set(attributes?.map(attr => `${attr.attribute_type}|${attr.attribute_value}`))
    ).map(combined => {
      const [attribute_type, attribute_value] = combined.split('|')
      return { attribute_type, attribute_value }
    }).sort((a, b) => {
      // Sort by type first, then by value
      if (a.attribute_type !== b.attribute_type) {
        return a.attribute_type.localeCompare(b.attribute_type)
      }
      return a.attribute_value.localeCompare(b.attribute_value)
    })

    return NextResponse.json({
      attributes: uniqueAttributes,
      business_type_id: businessTypeIdNum,
      allowed_types: allowedAttributeTypes
    })
  } catch (error) {
    console.error('Error in GET /api/business-admin/available-attributes:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
