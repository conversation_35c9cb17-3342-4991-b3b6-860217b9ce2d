"use client"

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ReviewSelectionModal } from '@/app/messages/components/ReviewSelectionModal'
import { Star, Package, Store, Truck } from 'lucide-react'

export default function TestReviewsPage() {
  const [showReviewModal, setShowReviewModal] = useState(false)
  const [reviewType, setReviewType] = useState<'order' | 'business' | 'driver'>('order')

  // Mock user for testing
  const mockUser = {
    id: 'test-user-123',
    email: '<EMAIL>'
  }

  const handleReviewSubmitted = (reviewData: any) => {
    console.log('Review submitted:', reviewData)
    alert(`Review submitted successfully!\nRating: ${reviewData.rating} stars\nComment: ${reviewData.comment || 'No comment'}`)
  }

  const openReviewModal = (type: 'order' | 'business' | 'driver') => {
    setReviewType(type)
    setShowReviewModal(true)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Review System Test</h1>
          <p className="text-gray-600">Test the review selection and submission functionality</p>
        </div>

        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => openReviewModal('order')}>
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Package className="h-8 w-8 text-blue-600" />
              </div>
              <CardTitle className="text-lg">Order Review</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-gray-600 mb-4">Review the food quality and overall order experience</p>
              <Button className="w-full">
                <Star className="h-4 w-4 mr-2" />
                Review Order
              </Button>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => openReviewModal('business')}>
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Store className="h-8 w-8 text-green-600" />
              </div>
              <CardTitle className="text-lg">Business Review</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-gray-600 mb-4">Review the business service and overall experience</p>
              <Button className="w-full">
                <Star className="h-4 w-4 mr-2" />
                Review Business
              </Button>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => openReviewModal('driver')}>
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Truck className="h-8 w-8 text-orange-600" />
              </div>
              <CardTitle className="text-lg">Driver Review</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-gray-600 mb-4">Review the delivery driver service and experience</p>
              <Button className="w-full">
                <Star className="h-4 w-4 mr-2" />
                Review Driver
              </Button>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>How it works</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-blue-600 font-semibold text-sm">1</span>
              </div>
              <div>
                <h4 className="font-medium">Select Review Type</h4>
                <p className="text-gray-600 text-sm">Choose whether you want to review an order, business, or driver</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-blue-600 font-semibold text-sm">2</span>
              </div>
              <div>
                <h4 className="font-medium">Choose Recent Order</h4>
                <p className="text-gray-600 text-sm">Select from your recent completed orders (last 7 days)</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-blue-600 font-semibold text-sm">3</span>
              </div>
              <div>
                <h4 className="font-medium">Rate & Comment</h4>
                <p className="text-gray-600 text-sm">Give a star rating (1-5) and optionally add a comment</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-blue-600 font-semibold text-sm">4</span>
              </div>
              <div>
                <h4 className="font-medium">Submit Review</h4>
                <p className="text-gray-600 text-sm">Your review is saved and helps improve the service</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="mt-8 text-center">
          <p className="text-gray-500 text-sm">
            This test page demonstrates the review selection workflow that integrates with the messaging system.
          </p>
        </div>

        <ReviewSelectionModal
          isOpen={showReviewModal}
          onClose={() => setShowReviewModal(false)}
          reviewType={reviewType}
          user={mockUser}
          onReviewSubmitted={handleReviewSubmitted}
        />
      </div>
    </div>
  )
}
