<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Selector API Tests</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        .business-selector { margin: 10px 0; }
        select { padding: 5px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Business Selector API Tests</h1>
    
    <div class="test-section">
        <h2>1. Authentication Test</h2>
        <button onclick="testAuth()">Test Authentication</button>
        <div id="auth-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Available Businesses Test</h2>
        <button onclick="testAvailableBusinesses()">Fetch Available Businesses</button>
        <div id="businesses-result"></div>
        
        <div class="business-selector">
            <label>Select Business for Testing:</label>
            <select id="business-selector" onchange="updateSelectedBusiness()">
                <option value="">Select a business...</option>
            </select>
            <span id="selected-business-info"></span>
        </div>
    </div>

    <div class="test-section">
        <h2>3. Business Data API Tests</h2>
        <button onclick="testBusinessData()">Test /api/business-admin/business</button>
        <button onclick="testCustomCategories()">Test /api/business-admin/custom-categories</button>
        <button onclick="testCategorySubscriptions()">Test /api/business-admin/category-subscriptions</button>
        <button onclick="testBusinessAttributes()">Test /api/business-admin/attributes</button>
        <div id="api-results"></div>
    </div>

    <div class="test-section">
        <h2>4. Comparison Test</h2>
        <button onclick="runComparisonTest()">Compare All Businesses</button>
        <div id="comparison-result"></div>
    </div>

    <script>
        let authToken = '';
        let availableBusinesses = [];
        let selectedBusinessId = null;

        // Get auth token from localStorage
        function getAuthToken() {
            authToken = localStorage.getItem('loop_jersey_auth_token') || '';
            return authToken;
        }

        // Test authentication
        async function testAuth() {
            const resultDiv = document.getElementById('auth-result');
            const token = getAuthToken();
            
            if (!token) {
                resultDiv.innerHTML = '<div class="error">No auth token found in localStorage. Please log in first.</div>';
                return false;
            }
            
            resultDiv.innerHTML = `<div class="success">Auth token found: ${token.substring(0, 20)}...</div>`;
            return true;
        }

        // Test available businesses
        async function testAvailableBusinesses() {
            const resultDiv = document.getElementById('businesses-result');
            const selector = document.getElementById('business-selector');
            
            if (!await testAuth()) return;

            try {
                const response = await fetch('/api/admin/businesses-direct', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                availableBusinesses = Array.isArray(data) ? data : data.businesses || [];
                
                // Populate selector
                selector.innerHTML = '<option value="">Select a business...</option>';
                availableBusinesses.forEach(business => {
                    const option = document.createElement('option');
                    option.value = business.id;
                    option.textContent = `${business.name} (ID: ${business.id})`;
                    selector.appendChild(option);
                });

                resultDiv.innerHTML = `
                    <div class="success">Found ${availableBusinesses.length} businesses</div>
                    <pre>${JSON.stringify(availableBusinesses, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }

        // Update selected business
        function updateSelectedBusiness() {
            const selector = document.getElementById('business-selector');
            const infoSpan = document.getElementById('selected-business-info');
            
            selectedBusinessId = selector.value ? parseInt(selector.value) : null;
            
            if (selectedBusinessId) {
                const business = availableBusinesses.find(b => b.id === selectedBusinessId);
                infoSpan.textContent = business ? `Selected: ${business.name}` : '';
                
                // Store in localStorage like the real app does
                localStorage.setItem('loop_jersey_selected_business_id', selectedBusinessId.toString());
            } else {
                infoSpan.textContent = '';
                localStorage.removeItem('loop_jersey_selected_business_id');
            }
        }

        // Test business data API
        async function testBusinessData() {
            await testAPI('/api/business-admin/business', 'Business Data');
        }

        // Test custom categories API
        async function testCustomCategories() {
            await testAPI('/api/business-admin/custom-categories', 'Custom Categories');
        }

        // Test category subscriptions API
        async function testCategorySubscriptions() {
            await testAPI('/api/business-admin/category-subscriptions', 'Category Subscriptions');
        }

        // Test business attributes API
        async function testBusinessAttributes() {
            await testAPI('/api/business-admin/attributes', 'Business Attributes');
        }

        // Generic API test function
        async function testAPI(endpoint, name) {
            const resultDiv = document.getElementById('api-results');
            
            if (!selectedBusinessId) {
                resultDiv.innerHTML += `<div class="error">${name}: Please select a business first</div>`;
                return;
            }

            try {
                // Test without businessId parameter
                const urlWithoutParam = endpoint;
                const responseWithoutParam = await fetch(urlWithoutParam, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Cache-Control': 'no-cache'
                    }
                });

                // Test with businessId parameter
                const urlWithParam = `${endpoint}?businessId=${selectedBusinessId}`;
                const responseWithParam = await fetch(urlWithParam, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Cache-Control': 'no-cache'
                    }
                });

                const dataWithoutParam = responseWithoutParam.ok ? await responseWithoutParam.json() : null;
                const dataWithParam = responseWithParam.ok ? await responseWithParam.json() : null;

                resultDiv.innerHTML += `
                    <div class="info">
                        <h4>${name} Test Results</h4>
                        <p><strong>Without businessId:</strong> ${responseWithoutParam.status} ${responseWithoutParam.statusText}</p>
                        <p><strong>With businessId=${selectedBusinessId}:</strong> ${responseWithParam.status} ${responseWithParam.statusText}</p>
                        
                        <details>
                            <summary>Response without businessId</summary>
                            <pre>${JSON.stringify(dataWithoutParam, null, 2)}</pre>
                        </details>
                        
                        <details>
                            <summary>Response with businessId</summary>
                            <pre>${JSON.stringify(dataWithParam, null, 2)}</pre>
                        </details>
                        
                        <p><strong>Are responses different?</strong> ${JSON.stringify(dataWithoutParam) !== JSON.stringify(dataWithParam) ? 'YES' : 'NO'}</p>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML += `<div class="error">${name}: ${error.message}</div>`;
            }
        }

        // Run comparison test across all businesses
        async function runComparisonTest() {
            const resultDiv = document.getElementById('comparison-result');
            
            if (availableBusinesses.length === 0) {
                resultDiv.innerHTML = '<div class="error">Please fetch available businesses first</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">Running comparison test...</div>';

            const results = {};
            
            for (const business of availableBusinesses) {
                try {
                    // Test custom categories for each business
                    const response = await fetch(`/api/business-admin/custom-categories?businessId=${business.id}`, {
                        headers: {
                            'Authorization': `Bearer ${authToken}`,
                            'Cache-Control': 'no-cache'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        results[business.id] = {
                            name: business.name,
                            categoriesCount: data.categories ? data.categories.length : 0,
                            categories: data.categories || [],
                            rawResponse: data
                        };
                    } else {
                        results[business.id] = {
                            name: business.name,
                            error: `HTTP ${response.status}: ${response.statusText}`
                        };
                    }
                } catch (error) {
                    results[business.id] = {
                        name: business.name,
                        error: error.message
                    };
                }
            }

            // Display comparison results
            let html = '<h4>Custom Categories Comparison</h4>';
            
            // Summary table
            html += '<table border="1" style="border-collapse: collapse; width: 100%;">';
            html += '<tr><th>Business ID</th><th>Business Name</th><th>Categories Count</th><th>Status</th></tr>';
            
            for (const [businessId, result] of Object.entries(results)) {
                html += `<tr>
                    <td>${businessId}</td>
                    <td>${result.name}</td>
                    <td>${result.error ? 'Error' : result.categoriesCount}</td>
                    <td>${result.error ? result.error : 'Success'}</td>
                </tr>`;
            }
            html += '</table>';

            // Detailed results
            html += '<h4>Detailed Results</h4>';
            for (const [businessId, result] of Object.entries(results)) {
                html += `
                    <details>
                        <summary>${result.name} (ID: ${businessId}) - ${result.error ? 'Error' : result.categoriesCount + ' categories'}</summary>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </details>
                `;
            }

            // Check if all results are identical
            const responseStrings = Object.values(results).map(r => JSON.stringify(r.rawResponse));
            const allIdentical = responseStrings.every(str => str === responseStrings[0]);
            
            html += `<div class="${allIdentical ? 'error' : 'success'}">
                <strong>All responses identical:</strong> ${allIdentical ? 'YES (This is the problem!)' : 'NO (Good!)'}
            </div>`;

            resultDiv.innerHTML = html;
        }

        // Initialize
        window.onload = function() {
            getAuthToken();
        };
    </script>
</body>
</html>
