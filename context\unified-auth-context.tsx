"use client"

import { createContext, useContext, useEffect, useState } from "react"
import { Session, User } from "@supabase/supabase-js"
import { supabase } from "@/lib/supabase"

// Define user role types
type UserRole = 'customer' | 'business_staff' | 'business_manager' | 'admin' | 'super_admin';

// Define user profile type
type UserProfile = {
  id: number;
  name: string;
  email?: string;
  role: UserRole;
  phone?: string;
  address?: string;
  auth_id?: string;
  first_name?: string;
  last_name?: string;
  postcode?: string;
  coordinates?: string;
  permissions?: string[];
};

// Define authentication context type
type AuthContextType = {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  userProfile: UserProfile | null;
  userRole: UserRole | null;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  isBusinessManager: boolean;
  isBusinessStaff: boolean;
  signUp: (email: string, password: string, userData: any) => Promise<{
    error: any | null;
    data: any | null;
  }>;
  signIn: (email: string, password: string) => Promise<{
    error: any | null;
    data: any | null;
  }>;
  signOut: () => Promise<void>;
  refreshUserProfile: () => Promise<UserProfile | null>;
}

// Create the authentication context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Create the authentication provider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  // State for user, session, and loading status
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [userRole, setUserRole] = useState<UserRole | null>(null);

  // Function to fetch user profile from the database using the API
  const fetchUserProfile = async (email: string): Promise<UserProfile | null> => {
    try {
      console.log("Fetching user profile for:", email);

      // No hardcoded email checks - use database roles

      // Get the current session for authentication
      let currentSession = null;
      try {
        const { data: { session } } = await supabase.auth.getSession();
        currentSession = session;
      } catch (sessionError) {
        console.warn("Session fetch failed:", sessionError);
        return null;
      }

      if (!currentSession) {
        console.warn("No active session, cannot fetch user profile");
        return null;
      }

      // Get proper authentication headers
      const { getAuthHeaders } = await import('@/utils/auth-utils');
      const headers = await getAuthHeaders({
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      });

      // Use the API endpoint instead of direct Supabase access
      const response = await fetch('/api/user/profile', {
        method: 'GET',
        credentials: 'include',
        headers
      });

      if (!response.ok) {
        console.error("Error fetching user profile from API:", response.status, response.statusText);
        return null;
      }

      const result = await response.json();
      console.log("User profile API response:", result);

      if (!result.data) {
        console.warn("No user profile found for email:", email);
        return null;
      }

      // User profile found, but we don't need to log the details
      return result.data as UserProfile;
    } catch (error) {
      console.error("Unexpected error fetching user profile:", error);
      return null;
    }
  };

  // Function to refresh the user profile
  const refreshUserProfile = async (): Promise<UserProfile | null> => {
    if (!user?.email) {
      console.warn("Cannot refresh user profile: No user email available");
      return null;
    }

    const profile = await fetchUserProfile(user.email);

    if (profile) {
      setUserProfile(profile);
      setUserRole(profile.role);
    }

    return profile;
  };

  // Initialize authentication state
  useEffect(() => {
    let isMounted = true;
    let timeoutId: NodeJS.Timeout | null = null;

    // Add event listener for auth-loading-clear event
    const handleAuthLoadingClear = (event: Event) => {
      try {
        console.log("Auth loading clear event received");
        if (isMounted && isLoading) {
          console.log("Clearing auth loading state due to event");
          setIsLoading(false);
        }
      } catch (error) {
        console.error("Error handling auth loading clear event:", error);
      }
    };

    // Add event listener for successful login
    const handleLoginSuccess = async (event: Event) => {
      try {
        console.log("Login success event received");
        const customEvent = event as CustomEvent;
        if (isMounted && customEvent.detail?.user) {
          try {
            // Force store authentication data
            const { forceStoreAuthData } = await import('@/utils/auth-utils');
            if (customEvent.detail.session) {
              await forceStoreAuthData(customEvent.detail.session);
            }

            // Update user state immediately
            setUser(customEvent.detail.user);
            setIsLoading(false);

            console.log("Login success handled successfully");
          } catch (error) {
            console.error("Error handling login success:", error);
          }
        }
      } catch (error) {
        console.error("Error in login success event handler:", error);
      }
    };

    window.addEventListener('auth-loading-clear', handleAuthLoadingClear);
    window.addEventListener('auth-login-success', handleLoginSuccess);

    // Function to get the current session
    const getSession = async () => {
      try {
        setIsLoading(true);

        // Set a timeout to prevent the loading state from getting stuck
        timeoutId = setTimeout(() => {
          if (isMounted && isLoading) {
            console.warn("Auth loading state timed out after 5 seconds");
            setIsLoading(false);

            // If we have a stored user, use it even if the session check timed out
            if (typeof window !== 'undefined') {
              const storedUserData = localStorage.getItem('loop_jersey_user');
              if (storedUserData && typeof storedUserData === 'string' && storedUserData.trim() !== '') {
                try {
                  const parsedUser = JSON.parse(storedUserData);
                  if (parsedUser && typeof parsedUser === 'object') {
                    console.log("Using stored user data after timeout:", parsedUser.email || 'No email');
                    setUser(parsedUser);
                  }
                } catch (parseError) {
                  console.error("Error parsing stored user data after timeout:", parseError);
                }
              }
            }
          }
        }, 5000); // 5 second timeout (reduced from 10 seconds)

        // Check for stored user data in localStorage first for faster initial state
        if (typeof window !== 'undefined') {
          const sessionActive = localStorage.getItem('loop_jersey_session_active');
          const storedUserData = localStorage.getItem('loop_jersey_user');

          // Make sure storedUserData is a non-empty string before trying to parse it
          if (sessionActive === 'true' && storedUserData && typeof storedUserData === 'string' && storedUserData.trim() !== '') {
            try {
              const parsedUser = JSON.parse(storedUserData);

              // Additional validation to ensure parsedUser is an object with expected properties
              if (parsedUser && typeof parsedUser === 'object') {
                console.log("Found stored user data in localStorage:", parsedUser.email || 'No email');

                // Set user immediately for faster UI response
                if (isMounted) {
                  setUser(parsedUser);
                }
              } else {
                console.warn("Invalid user data format in localStorage");
                // Clear invalid data
                localStorage.removeItem('loop_jersey_user');
              }
            } catch (parseError) {
              console.error("Error parsing stored user data:", parseError);
              // Clear invalid data
              localStorage.removeItem('loop_jersey_user');
            }
          }
        }

        // Get the current session from Supabase
        const { data: { session: currentSession }, error } = await supabase.auth.getSession();

        if (error) {
          console.error("Error getting session:", error.message);
          if (isMounted) {
            setSession(null);
            setUser(null);
            setUserProfile(null);
            setUserRole(null);
          }
          return;
        }

        if (isMounted) {
          setSession(currentSession);
          setUser(currentSession?.user ?? null);

          // Fetch user profile if we have a user
          if (currentSession?.user?.email) {
            try {
              const profile = await fetchUserProfile(currentSession.user.email);

              if (profile && isMounted) {
                setUserProfile(profile);
                setUserRole(profile.role);

                // Store user profile in localStorage for faster access next time
                if (typeof window !== 'undefined' && profile && typeof profile === 'object') {
                  try {
                    const profileJson = JSON.stringify(profile);
                    // Verify the JSON string is valid before storing
                    JSON.parse(profileJson); // This will throw if invalid
                    localStorage.setItem('loop_jersey_user_profile', profileJson);
                  } catch (storageError) {
                    console.error("Error storing user profile:", storageError);
                  }
                }
              }
            } catch (profileError) {
              console.error("Error fetching user profile:", profileError);
              // No hardcoded fallbacks - rely on database roles
            }
          } else {
            // No user email in session, clear profile
            setUserProfile(null);
            setUserRole(null);
          }
        }
      } catch (error) {
        console.error("Unexpected error getting session:", error);
        // Ensure we don't leave the app in a loading state on error
        if (isMounted) {
          setSession(null);
          setUser(null);
          setUserProfile(null);
          setUserRole(null);
        }
      } finally {
        // Clear the timeout if it's still active
        if (timeoutId) clearTimeout(timeoutId);

        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    // Get the initial session
    getSession();

    // Set up auth state change listener
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, newSession) => {

        if (!isMounted) return;

        // Set loading state for auth changes
        setIsLoading(true);

        try {
          if (newSession) {
            // Update session and user
            setSession(newSession);
            setUser(newSession.user);

            // Store session data in localStorage for faster access and better persistence
            if (typeof window !== 'undefined') {
              try {
                // Store token using our utility
                const { storeAuthToken } = await import('@/utils/auth-token');
                storeAuthToken(newSession.access_token);

                // Set session active flag
                localStorage.setItem('loop_jersey_session_active', 'true');

                // Store user data - only if user object is valid
                if (newSession.user && typeof newSession.user === 'object') {
                  try {
                    const userJson = JSON.stringify(newSession.user);
                    // Verify the JSON string is valid before storing
                    JSON.parse(userJson); // This will throw if invalid
                    localStorage.setItem('loop_jersey_user', userJson);
                  } catch (jsonError) {
                    // Silently handle JSON serialization errors
                  }
                }
              } catch (storageError) {
                // Silently handle storage errors
              }
            }

            // Fetch user profile if we have a user
            if (newSession.user?.email) {
              try {
                const profile = await fetchUserProfile(newSession.user.email);

                if (profile && isMounted) {
                  setUserProfile(profile);
                  setUserRole(profile.role);

                  // Store profile in localStorage
                  if (typeof window !== 'undefined' && profile && typeof profile === 'object') {
                    try {
                      const profileJson = JSON.stringify(profile);
                      // Verify the JSON string is valid before storing
                      JSON.parse(profileJson); // This will throw if invalid
                      localStorage.setItem('loop_jersey_user_profile', profileJson);
                    } catch (storageError) {
                      // Silently handle storage errors
                    }
                  }
                } else if (newSession.user.email === '<EMAIL>' && isMounted) {
                  // Special fallback for super admin if profile fetch completely fails
                  console.log("Profile fetch failed for super admin in auth change, using emergency fallback");
                  const fallbackProfile: UserProfile = {
                    id: 0,
                    name: 'Paul Cookson',
                    email: newSession.user.email,
                    role: 'super_admin',
                    phone: undefined,
                    address: undefined,
                    auth_id: newSession.user.id
                  };
                  setUserProfile(fallbackProfile);
                  setUserRole('super_admin');
                }
              } catch (profileError) {
                // Emergency fallback for super admin
                if (newSession.user.email === '<EMAIL>' && isMounted) {
                  console.log("Profile error for super admin in auth change, using emergency fallback");
                  const fallbackProfile: UserProfile = {
                    id: 0,
                    name: 'Paul Cookson',
                    email: newSession.user.email,
                    role: 'super_admin',
                    phone: undefined,
                    address: undefined,
                    auth_id: newSession.user.id
                  };
                  setUserProfile(fallbackProfile);
                  setUserRole('super_admin');
                }
              }
            }
          } else if (event === 'SIGNED_OUT') {
            // Clear auth state on sign out
            setSession(null);
            setUser(null);
            setUserProfile(null);
            setUserRole(null);

            // Clear localStorage data
            if (typeof window !== 'undefined') {
              try {
                localStorage.removeItem('loop_jersey_session_active');
                localStorage.removeItem('loop_jersey_user');
                localStorage.removeItem('loop_jersey_user_profile');

                // Use our utility to clear the token
                const { clearAuthToken } = await import('@/utils/auth-token');
                clearAuthToken();
              } catch (clearError) {
                // Silently handle clear errors
              }
            }
          }
        } catch (error) {
          // Silently handle auth state change errors
        } finally {
          // Always ensure loading state is cleared
          if (isMounted) {
            setIsLoading(false);
          }
        }
      }
    );

    // Cleanup
    return () => {
      isMounted = false;
      if (timeoutId) clearTimeout(timeoutId);
      window.removeEventListener('auth-loading-clear', handleAuthLoadingClear);
      window.removeEventListener('auth-login-success', handleLoginSuccess as EventListener);
      authListener.subscription.unsubscribe();
    };
  }, []);

  // Sign up a new user
  const signUp = async (email: string, password: string, userData: any) => {
    try {
      console.log("Starting signup process with data:", { email, userData });

      // Sign up with Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
          data: {
            name: userData.name,
            phone: userData.phone,
          },
        },
      });

      console.log("Supabase auth signup result:", { authData, authError });

      if (authError) {
        console.error("Auth signup error details:", {
          message: authError.message,
          status: authError.status,
          name: authError.name
        });
        return { error: authError, data: null };
      }

      // If signup is successful, create a user profile
      if (authData.user) {
        try {
          // Create user profile using the API endpoint
          const response = await fetch('/api/user/create-profile', {
            method: 'POST',
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: email.toLowerCase(),
              name: userData.name,
              phone: userData.phone,
              role: 'customer', // Default role
              auth_id: authData.user.id,
            }),
          });

          if (!response.ok) {
            // Continue anyway, as the auth account was created
          }
        } catch (profileErr) {
          // Continue anyway, as the auth account was created
        }
      }

      return { data: authData, error: null };
    } catch (error) {
      return { error, data: null };
    }
  };

  // Sign in an existing user
  const signIn = async (email: string, password: string) => {
    try {

      // Sign in with email and password
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error("Sign-in error:", error.message);
        return { data: null, error };
      }

      console.log("Sign-in successful, session established");

      return { data, error: null };
    } catch (error) {
      console.error("Unexpected error during sign-in:", error);
      return { error, data: null };
    }
  };

  // Link business to user after account creation
  const linkBusinessToUser = async (businessId: number, userEmail: string) => {
    try {
      if (!user?.id) {
        throw new Error("User not authenticated");
      }

      console.log("Linking business", businessId, "to user", userEmail);

      const response = await fetch('/api/business/link-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessId,
          userEmail,
          authUserId: user.id
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to link business');
      }

      const result = await response.json();
      console.log("Business linking successful:", result);

      // Refresh user profile to get updated business associations
      await refreshUserProfile();

      return { success: true, data: result };
    } catch (error: any) {
      console.error("Error linking business to user:", error);
      return { success: false, error: error.message };
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      console.log("Starting sign-out process");

      // Clear state first for immediate UI feedback
      setSession(null);
      setUser(null);
      setUserProfile(null);
      setUserRole(null);

      // Import and use clearAuthToken to clear all tokens and cookies
      const { clearAuthToken } = await import('@/utils/auth-token');
      clearAuthToken();

      // Clear all Supabase-specific storage
      if (typeof window !== 'undefined') {
        try {
          // Clear session storage
          sessionStorage.clear();

          // Clear any Supabase cookies directly
          const projectRef = process.env.NEXT_PUBLIC_SUPABASE_URL?.split('//')[1]?.split('.')[0];
          if (projectRef) {
            document.cookie = `sb-${projectRef}-auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax`;
            document.cookie = `sb-${projectRef}-auth-token=; path=/; domain=${window.location.hostname}; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax`;
          }

          // Clear the Supabase cookie
          document.cookie = `loop-jersey-auth=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax`;
          document.cookie = `loop-jersey-auth=; path=/; domain=${window.location.hostname}; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax`;
        } catch (e) {
          console.error("Error clearing browser storage:", e);
        }
      }

      // Sign out from Supabase with the signOut option to revoke tokens
      await supabase.auth.signOut({ scope: 'global' });

      console.log("Sign-out successful, redirecting to home page");

      // Force reload the page to ensure clean state
      // Use a small timeout to ensure all async operations complete
      if (typeof window !== 'undefined') {
        setTimeout(() => {
          // Use replace instead of href to avoid adding to browser history
          window.location.replace('/');
        }, 100);
      }
    } catch (error) {
      console.error("Unexpected error during sign out:", error);

      // Even if there's an error, try to redirect to home page
      if (typeof window !== 'undefined') {
        window.location.replace('/');
      }
    }
  };

  // Create the context value
  // Check admin status based on permissions, not just role
  const hasAdminPermission = userProfile?.permissions?.includes('access_admin_panel') || false;
  const isAdmin = userRole === 'admin' || userRole === 'super_admin' || hasAdminPermission;
  const isSuperAdmin = userRole === 'super_admin';

  console.log('🔍 Auth Context - Creating value:', {
    userEmail: user?.email,
    userRole,
    hasAdminPermission,
    isAdmin,
    isSuperAdmin,
    userProfile: !!userProfile,
    permissions: userProfile?.permissions
  });

  const value = {
    user,
    session,
    isLoading,
    userProfile,
    userRole,
    isAdmin,
    isSuperAdmin,
    isBusinessManager: userRole === 'business_manager',
    isBusinessStaff: userRole === 'business_staff',
    signUp,
    signIn,
    signOut,
    refreshUserProfile,
    linkBusinessToUser,
  };

  // Provide the context to children
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Hook to use the authentication context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
