"use client"

import { useState, useEffect } from "react"
import { Smartphone } from "lucide-react"
import { DropdownMenuItem } from "@/components/ui/dropdown-menu"

interface PWAInstallButtonProps {
  className?: string
}

export default function PWAInstallButton({ className }: PWAInstallButtonProps) {
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null)
  const [isInstallable, setIsInstallable] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)

  useEffect(() => {
    // Check if the app is already installed
    const checkInstallStatus = () => {
      const isAppInstalled = window.matchMedia("(display-mode: standalone)").matches ||
                            (window.navigator as any).standalone === true
      setIsInstalled(isAppInstalled)
    }

    checkInstallStatus()

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      e.preventDefault()
      // Stash the event so it can be triggered later
      setDeferredPrompt(e)
      setIsInstallable(true)
    }

    window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt)

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true)
      setIsInstallable(false)
      setDeferredPrompt(null)
    }

    window.addEventListener("appinstalled", handleAppInstalled)

    return () => {
      window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt)
      window.removeEventListener("appinstalled", handleAppInstalled)
    }
  }, [])

  const handleInstall = async () => {
    if (!deferredPrompt) return

    // Show the install prompt
    deferredPrompt.prompt()

    // Wait for the user to respond to the prompt
    const choiceResult = await deferredPrompt.userChoice
    
    if (choiceResult.outcome === "accepted") {
      console.log("User accepted the install prompt")
    } else {
      console.log("User dismissed the install prompt")
    }
    
    // Clear the saved prompt since it can't be used again
    setDeferredPrompt(null)
    setIsInstallable(false)
  }

  // Don't show if already installed
  if (isInstalled) {
    return null
  }

  // Show install button if browser supports automatic install
  if (isInstallable && deferredPrompt) {
    return (
      <DropdownMenuItem
        className={`cursor-pointer ${className}`}
        onClick={handleInstall}
      >
        <Smartphone className="mr-2 h-4 w-4" />
        <span>Install Loop App</span>
      </DropdownMenuItem>
    )
  }

  // Show manual install instructions as fallback
  const handleManualInstall = () => {
    const userAgent = navigator.userAgent.toLowerCase()
    let instructions = ""

    if (userAgent.includes('chrome') || userAgent.includes('edge')) {
      instructions = "To install Loop:\n1. Click the menu (⋮) in your browser\n2. Select 'Install Loop' or 'Add to Home screen'"
    } else if (userAgent.includes('firefox')) {
      instructions = "To install Loop:\n1. Click the menu (☰) in your browser\n2. Select 'Install this site as an app'"
    } else if (userAgent.includes('safari')) {
      instructions = "To install Loop:\n1. Tap the Share button (□↗)\n2. Select 'Add to Home Screen'"
    } else {
      instructions = "To install Loop:\nLook for 'Install app' or 'Add to Home screen' in your browser menu"
    }

    alert(instructions)
  }

  return (
    <DropdownMenuItem
      className={`cursor-pointer ${className}`}
      onClick={handleManualInstall}
    >
      <Smartphone className="mr-2 h-4 w-4" />
      <span>Install Loop App</span>
    </DropdownMenuItem>
  )
}
