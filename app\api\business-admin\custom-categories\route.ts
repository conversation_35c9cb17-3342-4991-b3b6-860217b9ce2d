import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyBusinessAdminAccess } from '@/lib/simple-auth'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Create a Supabase client with service role key for admin operations
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

// Helper function to create slug from name
function createSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
}

// GET - Fetch custom categories for a business
export async function GET(request: NextRequest) {
  try {
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const { user, profile: userProfile } = accessCheck

    // Get business ID from query parameter for admin users, or from business_managers table for business managers
    const { searchParams } = new URL(request.url)
    const queryBusinessId = searchParams.get('businessId')
    let businessId: number | null = null

    if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
      // Admin users can access any business
      if (queryBusinessId) {
        businessId = parseInt(queryBusinessId)
      } else {
        // Default to first business if no businessId specified
        const { data: firstBusiness } = await adminClient
          .from('businesses')
          .select('id')
          .limit(1)
          .single()

        if (firstBusiness) {
          businessId = firstBusiness.id
        }
      }
    } else {
      // Check if user is a business manager
      const { data: managerData, error: managerError } = await adminClient
        .from("business_managers")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .single()

      if (managerData && !managerError) {
        businessId = managerData.business_id
      }
    }

    if (!businessId) {
      return NextResponse.json(
        { error: 'No business associated with this account' },
        { status: 400 }
      )
    }

    const { data: categories, error } = await adminClient
      .from('business_custom_categories')
      .select('id, business_id, name, slug, description, display_order, is_active, level, parent_category, created_at, updated_at')
      .eq('business_id', businessId)
      .eq('is_active', true)
      .order('level', { ascending: true })
      .order('display_order', { ascending: true })

    if (error) {
      console.error('Error fetching custom categories:', error)
      return NextResponse.json(
        { error: 'Failed to fetch custom categories' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      categories: categories || []
    })
  } catch (error) {
    console.error('Unhandled error in custom categories GET:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Create a new custom category
export async function POST(request: NextRequest) {
  try {
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const { user, profile: userProfile } = accessCheck

    // Get business ID from business_managers table
    let businessId: number | null = null

    // Check if user is a business manager
    const { data: managerData, error: managerError } = await adminClient
      .from("business_managers")
      .select("business_id")
      .eq("user_id", userProfile.id)
      .single()

    if (managerData && !managerError) {
      businessId = managerData.business_id
    } else if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
      // Admin users can access any business, default to first business for now
      const { data: firstBusiness } = await adminClient
        .from('businesses')
        .select('id')
        .limit(1)
        .single()

      if (firstBusiness) {
        businessId = firstBusiness.id
      }
    }

    if (!businessId) {
      return NextResponse.json(
        { error: 'No business associated with this account' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { name, description, level = 0, parent_category } = body

    if (!name) {
      return NextResponse.json(
        { error: 'Category name is required' },
        { status: 400 }
      )
    }

    // Validate level and parent_category relationship
    if (level === 1 && !parent_category) {
      return NextResponse.json(
        { error: 'Level 1 categories must have a parent category' },
        { status: 400 }
      )
    }

    if (level === 0 && parent_category) {
      return NextResponse.json(
        { error: 'Level 0 categories cannot have a parent category' },
        { status: 400 }
      )
    }

    // Create slug from name
    const slug = createSlug(name)

    // Get the next display order
    const { data: existingCategories } = await adminClient
      .from('business_custom_categories')
      .select('display_order')
      .eq('business_id', businessId)
      .order('display_order', { ascending: false })
      .limit(1)

    const nextDisplayOrder = existingCategories && existingCategories.length > 0
      ? (existingCategories[0].display_order || 0) + 10
      : 10

    const { data: newCategory, error } = await adminClient
      .from('business_custom_categories')
      .insert({
        business_id: businessId,
        name: name.trim(),
        slug,
        description: description?.trim() || null,
        display_order: nextDisplayOrder,
        is_active: true,
        level: level,
        parent_category: parent_category?.trim() || null
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating custom category:', error)

      // Handle unique constraint violation
      if (error.code === '23505') {
        return NextResponse.json(
          { error: 'A category with this name already exists for your business' },
          { status: 409 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to create custom category' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      category: newCategory,
      message: 'Custom category created successfully'
    })
  } catch (error) {
    console.error('Unhandled error in custom categories POST:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH - Update a custom category
export async function PATCH(request: NextRequest) {
  try {
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const { user, profile: userProfile } = accessCheck

    // Get business ID from business_managers table
    let businessId: number | null = null

    // Check if user is a business manager
    const { data: managerData, error: managerError } = await adminClient
      .from("business_managers")
      .select("business_id")
      .eq("user_id", userProfile.id)
      .single()

    if (managerData && !managerError) {
      businessId = managerData.business_id
    } else if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
      // Admin users can access any business, default to first business for now
      const { data: firstBusiness } = await adminClient
        .from('businesses')
        .select('id')
        .limit(1)
        .single()

      if (firstBusiness) {
        businessId = firstBusiness.id
      }
    }

    if (!businessId) {
      return NextResponse.json(
        { error: 'No business associated with this account' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { categoryId, name, description, displayOrder } = body

    if (!categoryId) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      )
    }

    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    if (name) {
      updateData.name = name.trim()
      updateData.slug = createSlug(name)
    }

    if (description !== undefined) {
      updateData.description = description?.trim() || null
    }

    if (displayOrder !== undefined) {
      updateData.display_order = displayOrder
    }

    const { data: updatedCategory, error } = await adminClient
      .from('business_custom_categories')
      .update(updateData)
      .eq('id', categoryId)
      .eq('business_id', businessId) // Ensure business can only update their own categories
      .select()
      .single()

    if (error) {
      console.error('Error updating custom category:', error)

      if (error.code === '23505') {
        return NextResponse.json(
          { error: 'A category with this name already exists for your business' },
          { status: 409 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to update custom category' },
        { status: 500 }
      )
    }

    if (!updatedCategory) {
      return NextResponse.json(
        { error: 'Category not found or you do not have permission to update it' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      category: updatedCategory,
      message: 'Custom category updated successfully'
    })
  } catch (error) {
    console.error('Unhandled error in custom categories PATCH:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE - Delete a custom category
export async function DELETE(request: NextRequest) {
  try {
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const { user, profile: userProfile } = accessCheck

    // Get business ID from business_managers table
    let businessId: number | null = null

    // Check if user is a business manager
    const { data: managerData, error: managerError } = await adminClient
      .from("business_managers")
      .select("business_id")
      .eq("user_id", userProfile.id)
      .single()

    if (managerData && !managerError) {
      businessId = managerData.business_id
    } else if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
      // Admin users can access any business, default to first business for now
      const { data: firstBusiness } = await adminClient
        .from('businesses')
        .select('id')
        .limit(1)
        .single()

      if (firstBusiness) {
        businessId = firstBusiness.id
      }
    }

    if (!businessId) {
      return NextResponse.json(
        { error: 'No business associated with this account' },
        { status: 400 }
      )
    }

    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('categoryId')

    if (!categoryId) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      )
    }

    // Soft delete by setting is_active to false
    const { data: deletedCategory, error } = await adminClient
      .from('business_custom_categories')
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', parseInt(categoryId))
      .eq('business_id', businessId) // Ensure business can only delete their own categories
      .select()
      .single()

    if (error) {
      console.error('Error deleting custom category:', error)
      return NextResponse.json(
        { error: 'Failed to delete custom category' },
        { status: 500 }
      )
    }

    if (!deletedCategory) {
      return NextResponse.json(
        { error: 'Category not found or you do not have permission to delete it' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      message: 'Custom category deleted successfully'
    })
  } catch (error) {
    console.error('Unhandled error in custom categories DELETE:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
