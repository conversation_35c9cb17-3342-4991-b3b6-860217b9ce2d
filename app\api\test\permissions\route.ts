import { NextResponse } from "next/server"
import { getUserPermissions, userHasPermission } from "@/lib/permissions-service"
import { adminClient } from "@/lib/supabase-admin"

export async function GET(request: Request) {
  try {
    const url = new URL(request.url)
    const userId = url.searchParams.get('userId')

    if (!userId) {
      return NextResponse.json({ error: 'userId parameter required' }, { status: 400 })
    }

    const userIdNum = parseInt(userId)
    if (isNaN(userIdNum)) {
      return NextResponse.json({ error: 'Invalid userId' }, { status: 400 })
    }

    // Test direct database queries
    const { data: user, error: userError } = await adminClient
      .from('users')
      .select('id, email, role')
      .eq('id', userIdNum)
      .single()

    const { data: rolePerms, error: rolePermsError } = await adminClient
      .from('role_permissions')
      .select(`
        permission_id,
        permissions!inner(name)
      `)
      .eq('role_name', user?.role || '')

    // Test permissions service
    const permissions = await getUserPermissions(userIdNum)
    const hasAdminAccess = await userHasPermission(userIdNum, 'access_admin_panel')

    return NextResponse.json({
      userId: userIdNum,
      user,
      userError,
      rolePerms,
      rolePermsError,
      permissions,
      hasAdminAccess
    })
  } catch (error) {
    console.error('Error testing permissions:', error)
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 })
  }
}
