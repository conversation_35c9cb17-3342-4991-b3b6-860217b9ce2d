-- Migration for message template usage tracking
-- This supports analytics and personalized template suggestions

-- Create table for tracking message template usage
CREATE TABLE IF NOT EXISTS message_template_usage (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  template_id VARCHAR(100) NOT NULL, -- References template ID from frontend
  variables JSONB DEFAULT '{}', -- Variables used in the template
  used_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Indexes for performance
  INDEX idx_template_usage_user_id (user_id),
  INDEX idx_template_usage_template_id (template_id),
  INDEX idx_template_usage_used_at (used_at)
);

-- Enable RLS
ALTER TABLE message_template_usage ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own template usage" ON message_template_usage
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own template usage" ON message_template_usage
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create view for template analytics (admin use)
CREATE OR REPLACE VIEW template_usage_analytics AS
SELECT 
  template_id,
  COUNT(*) as usage_count,
  COUNT(DISTINCT user_id) as unique_users,
  DATE_TRUNC('day', used_at) as usage_date,
  AVG(EXTRACT(EPOCH FROM (NOW() - used_at))) / 86400 as avg_days_ago
FROM message_template_usage
WHERE used_at >= NOW() - INTERVAL '30 days'
GROUP BY template_id, DATE_TRUNC('day', used_at)
ORDER BY usage_count DESC;

-- Create function to get personalized template suggestions
CREATE OR REPLACE FUNCTION get_template_suggestions(user_uuid UUID, limit_count INTEGER DEFAULT 5)
RETURNS TABLE (
  template_id VARCHAR(100),
  usage_count BIGINT,
  last_used TIMESTAMPTZ,
  suggestion_score NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    mtu.template_id,
    COUNT(*) as usage_count,
    MAX(mtu.used_at) as last_used,
    -- Score based on frequency and recency
    (COUNT(*) * 0.7 + 
     EXTRACT(EPOCH FROM (NOW() - MAX(mtu.used_at))) / -86400 * 0.3) as suggestion_score
  FROM message_template_usage mtu
  WHERE mtu.user_id = user_uuid
    AND mtu.used_at >= NOW() - INTERVAL '30 days'
  GROUP BY mtu.template_id
  ORDER BY suggestion_score DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT SELECT ON template_usage_analytics TO authenticated;
GRANT EXECUTE ON FUNCTION get_template_suggestions(UUID, INTEGER) TO authenticated;

-- Comments
COMMENT ON TABLE message_template_usage IS 'Tracks usage of message templates for analytics and personalization';
COMMENT ON COLUMN message_template_usage.template_id IS 'References template ID from frontend MESSAGE_TEMPLATES array';
COMMENT ON COLUMN message_template_usage.variables IS 'JSONB object containing variables used when template was applied';
COMMENT ON VIEW template_usage_analytics IS 'Aggregated template usage data for admin analytics';
COMMENT ON FUNCTION get_template_suggestions IS 'Returns personalized template suggestions based on user history';
