import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id, name, first_name')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select(`
        id,
        is_verified,
        is_active,
        vehicle_type,
        total_deliveries,
        average_rating,
        created_at
      `)
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    // Get driver status
    const { data: driverStatus } = await supabase
      .from('driver_status')
      .select(`
        is_on_delivery,
        is_on_shift,
        current_order_id,
        current_location_lat,
        current_location_lng,
        location_updated_at,
        last_status_change
      `)
      .eq('driver_id', driverProfile.id)
      .single()

    // Get current active shift if driver is on shift
    let currentShift = null
    if (driverStatus?.is_on_shift) {
      const { data: shiftData } = await supabase
        .from('driver_shifts')
        .select(`
          id,
          shift_start,
          created_at
        `)
        .eq('driver_id', driverProfile.id)
        .is('shift_end', null)
        .single()

      currentShift = shiftData
    }

    // Get current order if driver is on delivery
    let currentOrder = null
    if (driverStatus?.is_on_delivery && driverStatus?.current_order_id) {
      const { data: orderData } = await supabase
        .from('orders')
        .select(`
          id,
          order_number,
          business_name,
          customer_name,
          customer_phone,
          delivery_address,
          postcode,
          parish,
          delivery_type,
          total,
          status,
          ready_time,
          businesses!inner (
            id,
            name,
            address,
            phone
          )
        `)
        .eq('id', driverStatus.current_order_id)
        .eq('delivery_method', 'delivery') // Only delivery orders for drivers
        .neq('status', 'delivered') // Don't show delivered orders as active
        .neq('status', 'cancelled') // Don't show cancelled orders as active
        .single()

      // Only set currentOrder if we found a non-delivered order
      if (orderData) {
        currentOrder = orderData
      } else {
        // If the current order is delivered/cancelled, update driver status
        await supabase
          .from('driver_status')
          .update({
            is_on_delivery: false,
            current_order_id: null
          })
          .eq('driver_id', driverProfile.id)
      }
    }

    // Get today's earnings
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    const { data: todayEarnings } = await supabase
      .from('driver_earnings')
      .select('base_amount, tip_amount, bonus_amount')
      .eq('driver_id', driverProfile.id)
      .gte('created_at', today.toISOString())
      .lt('created_at', tomorrow.toISOString())

    const todayTotal = todayEarnings?.reduce((sum, earning) =>
      sum + (earning.base_amount || 0) + (earning.tip_amount || 0) + (earning.bonus_amount || 0), 0
    ) || 0

    // Get this week's earnings
    const startOfWeek = new Date(today)
    startOfWeek.setDate(today.getDate() - today.getDay()) // Start of week (Sunday)

    const { data: weekEarnings } = await supabase
      .from('driver_earnings')
      .select('base_amount, tip_amount, bonus_amount')
      .eq('driver_id', driverProfile.id)
      .gte('created_at', startOfWeek.toISOString())

    const weekTotal = weekEarnings?.reduce((sum, earning) =>
      sum + (earning.base_amount || 0) + (earning.tip_amount || 0) + (earning.bonus_amount || 0), 0
    ) || 0

    // Get today's delivery count
    const { count: todayDeliveries } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .eq('driver_id', driverProfile.id)
      .eq('delivery_method', 'delivery') // Only count delivery orders for drivers
      .eq('status', 'delivered')
      .gte('updated_at', today.toISOString())
      .lt('updated_at', tomorrow.toISOString())

    // Get available orders count (only from businesses driver is approved for)
    const { data: approvedBusinesses } = await supabase
      .from('driver_business_approvals')
      .select('business_id')
      .eq('driver_id', driverProfile.id)
      .eq('status', 'approved')

    let availableOrdersCount = 0
    if (approvedBusinesses && approvedBusinesses.length > 0) {
      const approvedBusinessIds = approvedBusinesses.map(approval => approval.business_id)
      const { count } = await supabase
        .from('orders')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'offered')
        .eq('delivery_method', 'delivery') // Only count delivery orders for drivers
        .is('driver_id', null)
        .in('business_id', approvedBusinessIds)
      availableOrdersCount = count || 0
    }

    // Get business approvals count
    const { count: approvedBusinessesCount } = await supabase
      .from('driver_business_approvals')
      .select('*', { count: 'exact', head: true })
      .eq('driver_id', driverProfile.id)
      .eq('status', 'approved')

    // Get recent deliveries (last 5)
    const { data: recentDeliveries } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_name,
        customer_name,
        delivery_fee,
        delivery_type,
        status,
        updated_at
      `)
      .eq('driver_id', driverProfile.id)
      .eq('delivery_method', 'delivery') // Only delivery orders for drivers
      .in('status', ['delivered', 'out_for_delivery', 'picked_up'])
      .order('updated_at', { ascending: false })
      .limit(5)

    // Calculate driver performance metrics
    const driverSince = new Date(driverProfile.created_at)
    const daysSinceJoined = Math.floor((Date.now() - driverSince.getTime()) / (1000 * 60 * 60 * 24))
    const averageDeliveriesPerDay = daysSinceJoined > 0 ?
      Math.round((driverProfile.total_deliveries / daysSinceJoined) * 10) / 10 : 0

    return NextResponse.json({
      driver: {
        id: driverProfile.id,
        authId: user.auth_id,
        name: user.name || user.first_name || userEmail.split('@')[0] || 'Driver',
        isVerified: driverProfile.is_verified,
        isActive: driverProfile.is_active,
        vehicleType: driverProfile.vehicle_type,
        totalDeliveries: driverProfile.total_deliveries,
        averageRating: driverProfile.average_rating,
        memberSince: driverProfile.created_at,
        averageDeliveriesPerDay
      },
      status: {
        isOnDelivery: (driverStatus?.is_on_delivery || false) && currentOrder !== null,
        isOnShift: driverStatus?.is_on_shift || false,
        lastStatusChange: driverStatus?.last_status_change,
        hasLocation: !!(driverStatus?.current_location_lat && driverStatus?.current_location_lng),
        locationUpdatedAt: driverStatus?.location_updated_at,
        approvedBusinesses: approvedBusinessesCount || 0
      },
      currentShift,
      currentOrder,
      earnings: {
        today: todayTotal,
        thisWeek: weekTotal,
        currency: 'GBP'
      },
      stats: {
        todayDeliveries: todayDeliveries || 0,
        availableOrders: availableOrdersCount || 0,
        totalDeliveries: driverProfile.total_deliveries,
        averageRating: driverProfile.average_rating
      },
      recentDeliveries: recentDeliveries || [],
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in driver dashboard API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
