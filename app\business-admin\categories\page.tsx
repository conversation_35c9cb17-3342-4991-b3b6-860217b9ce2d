"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Search, Tags, Layout, Settings, ArrowRight, Star, Filter, HelpCircle } from "lucide-react"
import { useRouter } from "next/navigation"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { useAuth } from "@/context/unified-auth-context"


interface CategoryStats {
  searchCategories: {
    subscribed: number
    total: number
    primary: string | null
  }
  customCategories: {
    total: number
    level0: number
    level1: number
  }
  layout: string
  attributes: number
}



export default function CategoriesHubPage() {
  const router = useRouter()
  const { user, isAdmin, isSuperAdmin } = useAuth()
  const [stats, setStats] = useState<CategoryStats | null>(null)
  const [loading, setLoading] = useState(true)

  // Admin business selection - read from unified layout
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(null)



  // Fetch category statistics
  const fetchStats = async () => {
    setLoading(true)
    try {
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      // Build URLs with business ID for admin users
      let searchUrl = '/api/business-admin/category-subscriptions'
      let customUrl = '/api/business-admin/custom-categories'
      let businessUrl = '/api/business-admin/business'
      let attributesUrl = '/api/business-admin/business-attributes'

      if (selectedBusinessId) {
        searchUrl += `?businessId=${selectedBusinessId}`
        customUrl += `?businessId=${selectedBusinessId}`
        businessUrl += `?businessId=${selectedBusinessId}`
        attributesUrl += `?businessId=${selectedBusinessId}`
      }

      // Fetch search categories stats
      console.log('Fetching search categories from:', searchUrl)
      const searchResponse = await fetch(searchUrl, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })
      console.log('Search response status:', searchResponse.status)

      let searchStats = { subscribed: 0, total: 0, primary: null }
      if (searchResponse.ok) {
        const searchData = await searchResponse.json()
        console.log('Search data received:', searchData)
        searchStats = {
          subscribed: searchData.filter((cat: any) => cat.is_subscribed).length,
          total: searchData.length,
          primary: searchData.find((cat: any) => cat.is_primary)?.name || null
        }
      } else {
        console.error('Search categories fetch failed:', searchResponse.status, await searchResponse.text())
      }

      // Fetch custom categories stats
      console.log('Fetching custom categories from:', customUrl)
      let customStats = { total: 0, level0: 0, level1: 0 }
      const customCategoriesResponse = await fetch(customUrl, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache'
        }
      })
      console.log('Custom categories response status:', customCategoriesResponse.status)
      if (customCategoriesResponse.ok) {
        const customCategoriesData = await customCategoriesResponse.json()
        console.log('Custom categories data received:', customCategoriesData)
        const categories = customCategoriesData.categories || []
        customStats = {
          total: categories.length,
          level0: categories.filter((cat: any) => cat.level === 0).length,
          level1: categories.filter((cat: any) => cat.level === 1).length
        }
      } else {
        console.error('Custom categories fetch failed:', customCategoriesResponse.status, await customCategoriesResponse.text())
      }

      // Fetch business layout
      console.log('Fetching business data from:', businessUrl)
      let layout = 'standard'
      const businessResponse = await fetch(businessUrl, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache'
        }
      })
      console.log('Business response status:', businessResponse.status)
      if (businessResponse.ok) {
        const businessData = await businessResponse.json()
        console.log('Business data received:', businessData)
        layout = businessData.business?.page_layout || 'standard'
      } else {
        console.error('Business fetch failed:', businessResponse.status, await businessResponse.text())
      }

      // Fetch attributes count
      console.log('Fetching attributes from:', attributesUrl)
      let attributes = 0
      const attributesResponse = await fetch(attributesUrl, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache'
        }
      })
      console.log('Attributes response status:', attributesResponse.status)
      if (attributesResponse.ok) {
        const attributesData = await attributesResponse.json()
        console.log('Attributes data received:', attributesData)
        attributes = attributesData.attributes?.length || 0
      } else {
        console.error('Attributes fetch failed:', attributesResponse.status, await attributesResponse.text())
      }

      const finalStats = {
        searchCategories: searchStats,
        customCategories: customStats,
        layout,
        attributes
      }
      console.log('Setting final stats:', finalStats)
      setStats(finalStats)
    } catch (error) {
      console.error('Error fetching stats:', error)
    } finally {
      setLoading(false)
    }
  }

  // Load selected business ID from localStorage (set by unified layout)
  useEffect(() => {
    try {
      const storedBusinessId = localStorage.getItem('loop_jersey_selected_business_id')
      if (storedBusinessId) {
        setSelectedBusinessId(parseInt(storedBusinessId))
      }
    } catch (e) {
      console.error('Error loading selected business ID:', e)
    }
  }, [])

  // Listen for business changes from unified layout
  useEffect(() => {
    const handleBusinessChange = (e: CustomEvent) => {
      const newBusinessId = e.detail.businessId;
      console.log('Categories hub - business changed via custom event:', newBusinessId);
      setSelectedBusinessId(newBusinessId);
    }

    window.addEventListener('businessChanged', handleBusinessChange as EventListener);
    return () => window.removeEventListener('businessChanged', handleBusinessChange as EventListener);
  }, [])

  useEffect(() => {
    if (user) {
      fetchStats()
    }
  }, [user, selectedBusinessId])



  const navigationCards = [
    {
      title: "Search Categories",
      description: "Subscribe to Loop platform categories to control when your business appears in customer search results",
      icon: Search,
      href: "/business-admin/categories/search",
      stats: stats ? `${stats.searchCategories.subscribed}/${stats.searchCategories.total} subscribed` : "Loading...",
      color: "emerald"
    },
    {
      title: "Page Categories",
      description: "Create and manage custom categories to organize your products on your business page",
      icon: Tags,
      href: "/business-admin/categories/page",
      stats: stats ? `${stats.customCategories.total} categories` : "Loading...",
      color: "blue"
    },
    {
      title: "Page Layout",
      description: "Choose how your business page appears to customers - standard or aisle layout",
      icon: Layout,
      href: "/business-admin/categories/layout",
      stats: stats ? `${stats.layout} layout` : "Loading...",
      color: "purple"
    },
    {
      title: "Business Attributes",
      description: "Manage your business specializations, features, and service offerings",
      icon: Settings,
      href: "/business-admin/categories/attributes",
      stats: stats ? `${stats.attributes} attributes` : "Loading...",
      color: "orange"
    }
  ]

  const getColorClasses = (color: string) => {
    const colors = {
      emerald: "bg-emerald-50 border-emerald-200 hover:bg-emerald-100",
      blue: "bg-blue-50 border-blue-200 hover:bg-blue-100",
      purple: "bg-purple-50 border-purple-200 hover:bg-purple-100",
      orange: "bg-orange-50 border-orange-200 hover:bg-orange-100"
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  const getIconColorClasses = (color: string) => {
    const colors = {
      emerald: "bg-emerald-100 text-emerald-600",
      blue: "bg-blue-100 text-blue-600",
      purple: "bg-purple-100 text-purple-600",
      orange: "bg-orange-100 text-orange-600"
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Categories</h1>
          <p className="text-gray-600 mt-1">
            Manage how your business appears in search results and organize your products
          </p>
        </div>

        <div className="flex items-center gap-2">

          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="icon">
                <HelpCircle className="h-4 w-4" />
              </Button>
            </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Categories Management Help</DialogTitle>
              <DialogDescription>
                Understanding the different types of category management
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Search Categories</h4>
                <p className="text-sm text-gray-600">
                  Subscribe to Loop platform categories to control when your business appears in customer search results.
                  These are predefined categories that customers can browse to find businesses like yours.
                </p>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Page Categories</h4>
                <p className="text-sm text-gray-600">
                  Create custom categories to organize your products on your business page (the red category buttons).
                  These help customers navigate your menu and find what they're looking for within your business.
                </p>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Page Layout</h4>
                <p className="text-sm text-gray-600">
                  Choose between standard layout (for restaurants/cafes) or aisle layout (for supermarkets/large inventories).
                  This affects how your products are displayed to customers.
                </p>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Business Attributes</h4>
                <p className="text-sm text-gray-600">
                  Manage your business specializations, features, and service offerings.
                  These help customers understand what makes your business unique.
                </p>
              </div>
            </div>
          </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Quick Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Search Visibility</p>
                  <p className="text-lg font-bold text-emerald-600">
                    {stats.searchCategories.subscribed} categories
                  </p>
                </div>
                <div className="h-8 w-8 bg-emerald-100 rounded-full flex items-center justify-center">
                  <Search className="h-4 w-4 text-emerald-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Primary Category</p>
                  <p className="text-sm font-semibold text-gray-900">
                    {stats.searchCategories.primary || 'None set'}
                  </p>
                </div>
                <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <Star className="h-4 w-4 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Page Layout</p>
                  <p className="text-sm font-semibold text-gray-900 capitalize">
                    {stats.layout}
                  </p>
                </div>
                <div className="h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <Layout className="h-4 w-4 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Custom Categories</p>
                  <p className="text-lg font-bold text-gray-900">
                    {stats.customCategories.total}
                  </p>
                </div>
                <div className="h-8 w-8 bg-orange-100 rounded-full flex items-center justify-center">
                  <Tags className="h-4 w-4 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Navigation Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {navigationCards.map((card) => {
          const Icon = card.icon
          return (
            <Card
              key={card.href}
              className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${getColorClasses(card.color)}`}
              onClick={() => router.push(card.href)}
            >
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <div className={`h-10 w-10 rounded-lg flex items-center justify-center ${getIconColorClasses(card.color)}`}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{card.title}</h3>
                        <p className="text-sm text-gray-600">{card.stats}</p>
                      </div>
                    </div>
                    <p className="text-gray-700 text-sm leading-relaxed mb-4">
                      {card.description}
                    </p>
                    <div className="flex items-center text-sm font-medium text-gray-900">
                      Manage {card.title.toLowerCase()}
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button
              variant="outline"
              className="justify-start h-auto p-4"
              onClick={() => router.push('/business-admin/categories/search')}
            >
              <div className="text-left">
                <div className="font-medium">Subscribe to Categories</div>
                <div className="text-sm text-gray-500">Improve search visibility</div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="justify-start h-auto p-4"
              onClick={() => router.push('/business-admin/categories/page')}
            >
              <div className="text-left">
                <div className="font-medium">Add Product Category</div>
                <div className="text-sm text-gray-500">Organize your products</div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="justify-start h-auto p-4"
              onClick={() => router.push('/business-admin/categories/layout')}
            >
              <div className="text-left">
                <div className="font-medium">Change Layout</div>
                <div className="text-sm text-gray-500">Update page appearance</div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="justify-start h-auto p-4"
              onClick={() => router.push('/business-admin/categories/attributes')}
            >
              <div className="text-left">
                <div className="font-medium">Update Attributes</div>
                <div className="text-sm text-gray-500">Manage specializations</div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
