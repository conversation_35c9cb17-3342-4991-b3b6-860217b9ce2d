-- Update driver functions for simplified shift system
-- Date: 2025-06-28

-- 1. Update end_driver_shift function to include shift_end_reason
CREATE OR REPLACE FUNCTION end_driver_shift(
  p_driver_id UUID,
  p_end_reason VARCHAR(20) DEFAULT 'manual'
)
RETURNS BOOLEAN AS $$
DECLARE
  v_shift_id INTEGER;
BEGIN
  -- Validate end reason
  IF p_end_reason NOT IN ('manual', 'app_closed', 'disconnection_timeout') THEN
    RAISE EXCEPTION 'Invalid shift end reason: %', p_end_reason;
  END IF;
  
  -- Get active shift
  SELECT id INTO v_shift_id
  FROM driver_shifts
  WHERE driver_id = p_driver_id AND shift_end IS NULL;
  
  IF v_shift_id IS NULL THEN
    RAISE EXCEPTION 'No active shift found for driver';
  END IF;
  
  -- Set shift end time and reason
  UPDATE driver_shifts
  SET 
    shift_end = NOW(),
    shift_end_reason = p_end_reason,
    updated_at = NOW()
  WHERE id = v_shift_id;
  
  -- Update driver status to off shift and clear location
  UPDATE driver_status
  SET 
    is_on_shift = FALSE,
    current_location_lat = NULL,
    current_location_lng = NULL,
    location_updated_at = NULL,
    last_status_change = NOW()
  WHERE driver_id = p_driver_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 2. Create handle_driver_disconnection function
CREATE OR REPLACE FUNCTION handle_driver_disconnection(
  p_driver_id UUID,
  p_grace_period_minutes INTEGER DEFAULT 5
)
RETURNS BOOLEAN AS $$
DECLARE
  v_is_on_shift BOOLEAN;
  v_last_activity TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Check if driver is currently on shift
  SELECT is_on_shift, last_status_change INTO v_is_on_shift, v_last_activity
  FROM driver_status
  WHERE driver_id = p_driver_id;
  
  -- Only process if driver is on shift
  IF NOT COALESCE(v_is_on_shift, FALSE) THEN
    RETURN FALSE;
  END IF;
  
  -- Check if grace period has expired
  IF v_last_activity + (p_grace_period_minutes || ' minutes')::INTERVAL < NOW() THEN
    -- End shift due to disconnection timeout
    PERFORM end_driver_shift(p_driver_id, 'disconnection_timeout');
    RETURN TRUE;
  END IF;
  
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- 3. Add constraint for shift_end_reason
ALTER TABLE driver_shifts ADD CONSTRAINT check_shift_end_reason 
  CHECK (shift_end_reason IN ('manual', 'app_closed', 'disconnection_timeout'));

-- 4. Add comments
COMMENT ON FUNCTION end_driver_shift(UUID, VARCHAR) IS 'End active shift with reason tracking for analytics';
COMMENT ON FUNCTION handle_driver_disconnection(UUID, INTEGER) IS 'Handle driver disconnection with configurable grace period before auto-ending shift';
