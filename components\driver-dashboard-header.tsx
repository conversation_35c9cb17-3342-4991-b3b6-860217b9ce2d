"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { User, Settings, Monitor, LogOut, HelpCircle } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import WheelLogoIcon from "@/components/wheel-logo-icon"
import Link from "next/link"
import { toast } from "sonner"

interface DriverData {
  id: string
  authId: string
  name: string
  isVerified: boolean
  isActive: boolean
  vehicleType: string
  totalDeliveries: number
  averageRating: number
  memberSince: string
  averageDeliveriesPerDay: number
}

interface DriverStatus {
  isOnShift: boolean
  isOnDelivery: boolean
  lastStatusChange: string | null
  hasLocation: boolean
  locationUpdatedAt: string | null
}

interface DriverDashboardHeaderProps {
  driver?: DriverData
  status?: DriverStatus
  onStatusChange?: (isOnShift: boolean) => void
  isUpdatingStatus?: boolean
}

export function DriverDashboardHeader({
  driver,
  status,
  onStatusChange,
  isUpdatingStatus = false
}: DriverDashboardHeaderProps) {
  const [driverData, setDriverData] = useState<DriverData | null>(driver || null)
  const [driverStatus, setDriverStatus] = useState<DriverStatus | null>(status || null)
  const [isLoading, setIsLoading] = useState(!driver || !status)

  // Fetch driver data if not provided
  useEffect(() => {
    if (!driver || !status) {
      fetchDriverData()
    }
  }, [driver, status])

  // Update local state when props change
  useEffect(() => {
    if (driver) {
      setDriverData(driver)
    }
  }, [driver])

  useEffect(() => {
    if (status) {
      setDriverStatus(status)
    }
  }, [status])

  const fetchDriverData = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/driver/dashboard')

      if (!response.ok) {
        throw new Error(`Failed to fetch driver data: ${response.statusText}`)
      }

      const data = await response.json()
      setDriverData(data.driver)
      setDriverStatus(data.status)
    } catch (err) {
      console.error('Error fetching driver data:', err)
      // Set fallback data to prevent header from breaking
      setDriverData({
        id: 'unknown',
        authId: 'unknown',
        name: 'Driver',
        isVerified: false,
        isActive: false,
        vehicleType: 'Unknown',
        totalDeliveries: 0,
        averageRating: 0,
        memberSince: new Date().toISOString(),
        averageDeliveriesPerDay: 0
      })
      setDriverStatus({
        isOnShift: false,
        isOnDelivery: false,
        lastStatusChange: null,
        hasLocation: false,
        locationUpdatedAt: null
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleStatusChange = async (checked: boolean) => {
    if (!driverStatus) return

    // If trying to end shift while on delivery, show warning
    if (!checked && driverStatus.isOnDelivery) {
      toast.error("Cannot end shift during delivery", {
        description: "Please complete your current delivery before ending your shift.",
        duration: 4000,
      })
      return
    }

    if (onStatusChange) {
      onStatusChange(checked)
    } else {
      // Default status update logic
      try {
        const response = await fetch('/api/driver/status', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ isOnShift: checked, action: 'toggle' }),
        })

        if (!response.ok) {
          throw new Error('Failed to update status')
        }

        setDriverStatus(prev => prev ? { ...prev, isOnShift: checked } : null)

        toast.success(checked ? "Shift started!" : "Shift ended!", {
          description: checked ? "You'll receive delivery requests" : "You won't receive new delivery requests",
        })
      } catch (err) {
        console.error('Error updating status:', err)
        toast.error("Failed to update status", {
          description: "Please try again",
        })
      }
    }
  }

  if (isLoading || !driverData || !driverStatus) {
    return (
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center bg-emerald-600 rounded-lg px-4 py-2.5 border border-emerald-500 shadow-sm h-10">
              <div className="wheel-logo mr-2">
                <WheelLogoIcon
                  size={24}
                  color="white"
                  className="text-white w-6 h-6"
                />
              </div>
              <span className="text-lg font-bold text-white">Loop</span>
            </div>
            <div className="border-l border-gray-300 pl-3">
              <div className="h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"></div>
              <div className="h-3 bg-gray-200 rounded w-20 animate-pulse"></div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="lg"
              className="relative h-10 w-10 rounded-lg text-white hover:bg-emerald-700 border border-gray-300 bg-emerald-600 p-0"
              asChild
            >
              <Link href="/driver-mobile/profile">
                <User className="h-5 w-5" />
              </Link>
            </Button>

            <div className="flex flex-col space-y-2">
              <div className="flex items-center space-x-2">
                <div className="h-6 w-10 bg-gray-200 rounded-full animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-12 animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white border-b border-gray-200 px-4 py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center bg-emerald-600 rounded-lg px-4 py-2.5 border border-emerald-500 shadow-sm h-10">
            <div className="wheel-logo mr-2">
              <WheelLogoIcon
                size={24}
                color="white"
                className="text-white w-6 h-6"
              />
            </div>
            <span className="text-lg font-bold text-white">Loop</span>
          </div>
          <div className="border-l border-gray-300 pl-3">
            <p className="font-semibold text-gray-900">{driverData.name}</p>
            <p className="text-xs text-gray-500">Driver ID: {driverData.authId?.slice(-6) || driverData.id.slice(-6)}</p>
          </div>
        </div>

        {/* Right Side - Status Toggle and Account */}
        <div className="flex items-center space-x-3">
          {/* Account Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="lg"
                className="relative h-10 w-10 rounded-lg text-white hover:bg-emerald-700 border border-gray-300 bg-emerald-600 p-0"
              >
                <User className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {driverData?.name || 'Driver'}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    Driver ID: {driverData?.authId?.slice(-6) || driverData?.id?.slice(-6) || 'Unknown'}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Link href="/driver-mobile/profile" className="cursor-pointer flex items-center">
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link href="/driver-mobile/settings" className="cursor-pointer flex items-center">
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link href="/driver" target="_blank" rel="noopener noreferrer" className="cursor-pointer flex items-center">
                  <Monitor className="mr-2 h-4 w-4" />
                  <span>Desktop Dashboard</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Link href="/driver-mobile/help" className="cursor-pointer flex items-center">
                  <HelpCircle className="mr-2 h-4 w-4" />
                  <span>Help & Support</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem disabled className="cursor-not-allowed">
                <LogOut className="mr-2 h-4 w-4" />
                <span>Sign Out (Coming Soon)</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Status Toggle */}
          <div className="flex flex-col space-y-2">
            <div className="flex items-center space-x-2 relative z-10">
              <Switch
                id="availability"
                checked={driverStatus.isOnShift}
                onCheckedChange={handleStatusChange}
                disabled={isUpdatingStatus}
                className="data-[state=checked]:bg-green-600 cursor-pointer relative z-20"
                style={{ pointerEvents: 'auto' }}
              />
              <Label
                htmlFor="availability"
                className="text-sm font-medium text-gray-700 cursor-pointer select-none"
                onClick={() => {
                  if (!isUpdatingStatus) {
                    handleStatusChange(!driverStatus.isOnShift)
                  }
                }}
              >
                {driverStatus.isOnShift ? "On Shift" : "Off Shift"}
              </Label>
            </div>

            {/* Delivery Status - Below Toggle */}
            {driverStatus.isOnDelivery && (
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="text-xs font-medium text-blue-700">On delivery</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
