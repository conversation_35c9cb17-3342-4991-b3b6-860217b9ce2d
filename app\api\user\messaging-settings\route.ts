import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyUserAccess } from '@/utils/auth-helpers'
import { MessagingSettings, DEFAULT_MESSAGING_SETTINGS } from '@/types/messaging-settings'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// GET - Get user's messaging settings
export async function GET(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user

    // Get user's connection profile
    const { data: profile, error } = await supabase
      .from('connection_profiles')
      .select('communication_preferences, display_name, is_public, allow_direct_messages')
      .eq('user_id', user.id)
      .single()

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching messaging settings:', error)
      return NextResponse.json(
        { error: 'Failed to fetch messaging settings' },
        { status: 500 }
      )
    }

    // If no profile exists, return default settings
    if (!profile) {
      return NextResponse.json({
        settings: DEFAULT_MESSAGING_SETTINGS,
        hasProfile: false
      })
    }

    // Extract messaging settings from communication_preferences
    const messagingSettings = profile.communication_preferences?.messaging || {}
    
    // Merge with defaults and profile data
    const settings: MessagingSettings = {
      ...DEFAULT_MESSAGING_SETTINGS,
      ...messagingSettings,
      public_username: profile.display_name || undefined,
      discoverable_by_username: profile.is_public || false,
      allow_business_messages: profile.allow_direct_messages !== false,
      allow_driver_messages: profile.allow_direct_messages !== false,
      allow_customer_messages: profile.allow_direct_messages !== false
    }

    return NextResponse.json({
      settings,
      hasProfile: true
    })

  } catch (error: any) {
    console.error('Error in messaging settings GET:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Update user's messaging settings
export async function POST(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user
    const settings: Partial<MessagingSettings> = await request.json()

    // Validate username if provided
    if (settings.public_username) {
      const username = settings.public_username.trim()
      
      // Basic validation
      if (username.length < 3 || username.length > 30) {
        return NextResponse.json(
          { error: 'Username must be between 3 and 30 characters' },
          { status: 400 }
        )
      }

      if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
        return NextResponse.json(
          { error: 'Username can only contain letters, numbers, underscores, and hyphens' },
          { status: 400 }
        )
      }

      // Check if username is already taken
      const { data: existingProfile } = await supabase
        .from('connection_profiles')
        .select('user_id')
        .eq('display_name', username)
        .neq('user_id', user.id)
        .single()

      if (existingProfile) {
        return NextResponse.json(
          { error: 'Username is already taken' },
          { status: 400 }
        )
      }
    }

    // Get current profile or create new one
    const { data: currentProfile } = await supabase
      .from('connection_profiles')
      .select('*')
      .eq('user_id', user.id)
      .single()

    const currentPreferences = currentProfile?.communication_preferences || {}
    
    // Update messaging preferences
    const updatedPreferences = {
      ...currentPreferences,
      messaging: {
        ...currentPreferences.messaging,
        messaging_enabled: settings.messaging_enabled,
        auto_accept_order_conversations: settings.auto_accept_order_conversations,
        allow_order_reviews: settings.allow_order_reviews,
        review_reminders: settings.review_reminders,
        message_history_retention_days: settings.message_history_retention_days,
        block_anonymous_messages: settings.block_anonymous_messages,
        show_real_name: settings.show_real_name
      }
    }

    const profileData = {
      user_id: user.id,
      profile_type: 'customer', // Default, can be updated later
      display_name: settings.public_username || currentProfile?.display_name,
      is_public: settings.discoverable_by_username ?? currentProfile?.is_public ?? false,
      allow_direct_messages: settings.allow_business_messages ?? 
                            settings.allow_driver_messages ?? 
                            settings.allow_customer_messages ?? 
                            currentProfile?.allow_direct_messages ?? true,
      communication_preferences: updatedPreferences,
      updated_at: new Date().toISOString()
    }

    let result
    if (currentProfile) {
      // Update existing profile
      const { data, error } = await supabase
        .from('connection_profiles')
        .update(profileData)
        .eq('user_id', user.id)
        .select()
        .single()

      if (error) {
        console.error('Error updating messaging settings:', error)
        return NextResponse.json(
          { error: 'Failed to update messaging settings' },
          { status: 500 }
        )
      }
      result = data
    } else {
      // Create new profile
      const { data, error } = await supabase
        .from('connection_profiles')
        .insert({
          ...profileData,
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating messaging profile:', error)
        return NextResponse.json(
          { error: 'Failed to create messaging profile' },
          { status: 500 }
        )
      }
      result = data
    }

    return NextResponse.json({
      success: true,
      profile: result
    })

  } catch (error: any) {
    console.error('Error in messaging settings POST:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
