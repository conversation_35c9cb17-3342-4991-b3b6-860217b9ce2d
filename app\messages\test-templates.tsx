"use client"

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { EnhancedQuickActions } from './components/EnhancedQuickActions'
import { MessageTemplateComposer } from './components/MessageTemplateComposer'
import { MESSAGE_TEMPLATES, QUICK_RESPONSES, MessageTemplate, QuickResponse } from '@/types/message-templates'

// Test page for message templates - can be accessed at /messages/test-templates
export default function TestTemplatesPage() {
  const [selectedTemplate, setSelectedTemplate] = useState<MessageTemplate | null>(null)
  const [showComposer, setShowComposer] = useState(false)
  const [testResults, setTestResults] = useState<string[]>([])

  // Mock user and context for testing
  const mockUser = {
    id: 'test-user-123',
    email: '<EMAIL>'
  }

  const mockContext = {
    order_id: 'order-456',
    business_id: 'business-789',
    rider_id: 'rider-101',
    role: 'customer' as const
  }

  const handleTemplateSelected = (template: MessageTemplate, context?: any) => {
    console.log('Template selected:', template, context)
    setSelectedTemplate(template)
    setShowComposer(true)
    addTestResult(`Template selected: ${template.title}`)
  }

  const handleQuickResponseSelected = (response: QuickResponse) => {
    console.log('Quick response selected:', response)
    addTestResult(`Quick response: ${response.text} ${response.emoji || ''}`)
  }

  const handleCustomActionSelected = (action: any) => {
    console.log('Custom action selected:', action)
    addTestResult(`Custom action: ${action.title}`)
  }

  const handleTemplateSend = (content: string, template: MessageTemplate) => {
    console.log('Template message sent:', { content, template })
    addTestResult(`Message sent: "${content.substring(0, 50)}${content.length > 50 ? '...' : ''}"`)
    setShowComposer(false)
    setSelectedTemplate(null)
  }

  const handleTemplateCancel = () => {
    setShowComposer(false)
    setSelectedTemplate(null)
    addTestResult('Template composition cancelled')
  }

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`])
  }

  const clearResults = () => {
    setTestResults([])
  }

  const testAllTemplates = () => {
    addTestResult('=== Testing All Templates ===')
    MESSAGE_TEMPLATES.forEach(template => {
      addTestResult(`Template: ${template.id} - ${template.title} (${template.category})`)
    })
  }

  const testQuickResponses = () => {
    addTestResult('=== Testing Quick Responses ===')
    QUICK_RESPONSES.forEach(response => {
      addTestResult(`Response: ${response.text} ${response.emoji || ''} (${response.category})`)
    })
  }

  const createTestData = async () => {
    try {
      addTestResult('Creating test data (recent completed order and business interaction)...')
      const response = await fetch('/api/test/create-test-data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ user_id: mockUser.id })
      })
      const data = await response.json()
      if (data.success) {
        addTestResult(`✅ Test data created: Order #${data.data.order_number}`)
        addTestResult('Refresh the page to see review quick actions!')
      } else {
        addTestResult(`❌ Error: ${data.error}`)
      }
    } catch (error) {
      addTestResult(`❌ Error creating test data: ${error}`)
    }
  }

  const cleanupTestData = async () => {
    try {
      addTestResult('Cleaning up test data...')
      const response = await fetch('/api/test/create-test-data', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ user_id: mockUser.id })
      })
      const data = await response.json()
      if (data.success) {
        addTestResult('✅ Test data cleaned up')
      } else {
        addTestResult(`❌ Error: ${data.error}`)
      }
    } catch (error) {
      addTestResult(`❌ Error cleaning up test data: ${error}`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Message Templates Test Page</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <Button onClick={testAllTemplates} variant="outline">
                Test All Templates
              </Button>
              <Button onClick={testQuickResponses} variant="outline">
                Test Quick Responses
              </Button>
              <Button onClick={createTestData} variant="outline" className="bg-green-50 hover:bg-green-100">
                Create Test Data
              </Button>
              <Button onClick={cleanupTestData} variant="outline" className="bg-red-50 hover:bg-red-100">
                Cleanup Test Data
              </Button>
              <Button onClick={clearResults} variant="outline">
                Clear Results
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Enhanced Quick Actions Test */}
          <Card>
            <CardHeader>
              <CardTitle>Enhanced Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <EnhancedQuickActions
                user={mockUser}
                context={mockContext}
                onTemplateSelected={handleTemplateSelected}
                onQuickResponseSelected={handleQuickResponseSelected}
                onCustomActionSelected={handleCustomActionSelected}
              />
            </CardContent>
          </Card>

          {/* Test Results */}
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-96 overflow-y-auto bg-gray-100 p-3 rounded text-sm font-mono">
                {testResults.length === 0 ? (
                  <p className="text-gray-500">No test results yet. Try the actions above.</p>
                ) : (
                  testResults.map((result, index) => (
                    <div key={index} className="mb-1">
                      {result}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Template Details */}
        <Card>
          <CardHeader>
            <CardTitle>Available Templates ({MESSAGE_TEMPLATES.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {MESSAGE_TEMPLATES.map(template => (
                <Card key={template.id} className="border">
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-sm">{template.title}</h4>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                          {template.category}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 line-clamp-2">
                        {template.content}
                      </p>
                      <div className="flex flex-wrap gap-1">
                        {template.user_roles.map(role => (
                          <span key={role} className="text-xs bg-blue-100 text-blue-800 px-1 py-0.5 rounded">
                            {role}
                          </span>
                        ))}
                      </div>
                      {template.variables && template.variables.length > 0 && (
                        <div className="text-xs text-gray-500">
                          Variables: {template.variables.join(', ')}
                        </div>
                      )}
                      <Button
                        size="sm"
                        variant="outline"
                        className="w-full text-xs"
                        onClick={() => handleTemplateSelected(template)}
                      >
                        Test Template
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Responses */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Responses ({QUICK_RESPONSES.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {QUICK_RESPONSES.map(response => (
                <Button
                  key={response.id}
                  size="sm"
                  variant="outline"
                  onClick={() => handleQuickResponseSelected(response)}
                  className="text-sm"
                >
                  {response.emoji && <span className="mr-1">{response.emoji}</span>}
                  {response.text}
                  <span className="ml-2 text-xs text-gray-500">({response.category})</span>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Template Composer Modal */}
      {showComposer && selectedTemplate && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <MessageTemplateComposer
            template={selectedTemplate}
            context={mockContext}
            onSend={handleTemplateSend}
            onCancel={handleTemplateCancel}
            className="max-w-lg w-full"
          />
        </div>
      )}
    </div>
  )
}
