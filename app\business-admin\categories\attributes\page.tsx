"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { toast } from 'sonner'
import {
  Plus,
  Trash2,
  RefreshCw,
  HelpCircle,
  Store,
  Utensils,
  Coffee,
  Pill,
  Car,
  CheckSquare
} from 'lucide-react'

interface BusinessAttribute {
  business_id: number
  attribute_type: string
  attribute_value: string
}

interface AvailableAttribute {
  attribute_type: string
  attribute_value: string
  description?: string
}

// Business type to attribute type mapping
const BUSINESS_TYPE_ATTRIBUTES: Record<number, { types: string[], icon: any, name: string }> = {
  1: { // Restaurant
    types: ['cuisine', 'restaurant_service_feature'],
    icon: Utensils,
    name: 'Restaurant'
  },
  2: { // Shop
    types: ['store_type', 'store_feature', 'delivery_option'],
    icon: Store,
    name: 'Shop'
  },
  3: { // Pharmacy
    types: ['pharmacy_type', 'pharmacy_service_feature'],
    icon: Pill,
    name: 'Pharmacy'
  },
  4: { // Cafe
    types: ['cafe_type', 'cafe_feature'],
    icon: Coffee,
    name: 'Cafe'
  },
  38: { // Errand
    types: ['errand_type', 'transport_type'],
    icon: CheckSquare,
    name: 'Errand Service'
  }
}

// Attribute type display names
const ATTRIBUTE_TYPE_NAMES: Record<string, string> = {
  'cuisine': 'Cuisine Types',
  'restaurant_service_feature': 'Service Features',
  'store_type': 'Store Types',
  'store_feature': 'Store Features',
  'delivery_option': 'Delivery Options',
  'pharmacy_type': 'Pharmacy Types',
  'pharmacy_service_feature': 'Service Features',
  'cafe_type': 'Cafe Types',
  'cafe_feature': 'Cafe Features',
  'errand_type': 'Service Types',
  'transport_type': 'Transport Options'
}

export default function AttributesPage() {
  const [businessAttributes, setBusinessAttributes] = useState<BusinessAttribute[]>([])
  const [availableAttributes, setAvailableAttributes] = useState<AvailableAttribute[]>([])
  const [businessType, setBusinessType] = useState<number | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")

  // Fetch business attributes and available options
  const fetchData = async () => {
    setLoading(true)
    try {
      const token = localStorage.getItem('loop_jersey_auth_token') || ''

      // Fetch current business attributes
      const attributesResponse = await fetch('/api/business-admin/attributes', {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache'
        }
      })

      if (attributesResponse.ok) {
        const attributesData = await attributesResponse.json()
        setBusinessAttributes(attributesData.attributes || [])
        setBusinessType(attributesData.business_type_id)

        // Fetch available attributes for this business type
        if (attributesData.business_type_id) {
          const availableResponse = await fetch(`/api/business-admin/available-attributes?businessTypeId=${attributesData.business_type_id}`, {
            headers: {
              'Authorization': token ? `Bearer ${token}` : '',
              'Cache-Control': 'no-cache'
            }
          })

          if (availableResponse.ok) {
            const availableData = await availableResponse.json()
            setAvailableAttributes(availableData.attributes || [])
          }
        }
      } else {
        toast.error('Failed to load attributes')
      }
    } catch (error) {
      console.error('Error fetching attributes:', error)
      toast.error('Failed to load attributes')
    } finally {
      setLoading(false)
    }
  }

  // Add attribute
  const addAttribute = async (attributeType: string, attributeValue: string) => {
    try {
      const token = localStorage.getItem('loop_jersey_auth_token') || ''

      const response = await fetch('/api/business-admin/attributes', {
        method: 'POST',
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          attribute_type: attributeType,
          attribute_value: attributeValue
        })
      })

      if (response.ok) {
        toast.success('Attribute added successfully')
        fetchData()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to add attribute')
      }
    } catch (error) {
      console.error('Error adding attribute:', error)
      toast.error('Failed to add attribute')
    }
  }

  // Remove attribute
  const removeAttribute = async (attributeType: string, attributeValue: string) => {
    try {
      const token = localStorage.getItem('loop_jersey_auth_token') || ''

      const response = await fetch('/api/business-admin/attributes', {
        method: 'DELETE',
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          attribute_type: attributeType,
          attribute_value: attributeValue
        })
      })

      if (response.ok) {
        toast.success('Attribute removed successfully')
        fetchData()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to remove attribute')
      }
    } catch (error) {
      console.error('Error removing attribute:', error)
      toast.error('Failed to remove attribute')
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  // Get business type configuration
  const businessTypeConfig = businessType ? BUSINESS_TYPE_ATTRIBUTES[businessType] : null
  const BusinessIcon = businessTypeConfig?.icon || Store

  // Group attributes by type
  const attributesByType = businessAttributes.reduce((acc, attr) => {
    if (!acc[attr.attribute_type]) {
      acc[attr.attribute_type] = []
    }
    acc[attr.attribute_type].push(attr.attribute_value)
    return acc
  }, {} as Record<string, string[]>)

  // Group available attributes by type
  const availableByType = availableAttributes.reduce((acc, attr) => {
    if (!acc[attr.attribute_type]) {
      acc[attr.attribute_type] = []
    }
    acc[attr.attribute_type].push(attr.attribute_value)
    return acc
  }, {} as Record<string, string[]>)

  // Filter available attributes by search term
  const filteredAvailableByType = Object.entries(availableByType).reduce((acc, [type, values]) => {
    const filteredValues = values.filter(value =>
      value.toLowerCase().includes(searchTerm.toLowerCase())
    )
    if (filteredValues.length > 0) {
      acc[type] = filteredValues
    }
    return acc
  }, {} as Record<string, string[]>)

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Business Attributes</h1>
            <p className="text-gray-600 mt-1">Loading...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Business Attributes</h1>
          <p className="text-gray-600 mt-1">
            Manage attributes that help customers find your business in search filters
          </p>
        </div>

        <div className="flex gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <HelpCircle className="h-4 w-4 mr-2" />
                Help
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Business Attributes Help</DialogTitle>
                <DialogDescription>
                  Understanding how business attributes work
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">What are Business Attributes?</h4>
                  <p className="text-sm text-gray-600">
                    Business attributes are contextual filters that appear on the left side of search results.
                    They help customers narrow down their search based on specific business characteristics.
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">How They Work</h4>
                  <p className="text-sm text-gray-600">
                    Unlike search categories (horizontal filters), attributes are business-type specific.
                    For example, restaurants have cuisine types and service features, while shops have
                    store types and product features.
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Best Practices</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Choose attributes that accurately describe your business</li>
                    <li>• Select attributes customers would logically filter by</li>
                    <li>• Don't over-select - focus on the most relevant attributes</li>
                    <li>• These appear as left-side filters in search results</li>
                  </ul>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Button variant="outline" onClick={fetchData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Business Type Info */}
      {businessTypeConfig && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                <BusinessIcon className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">{businessTypeConfig.name}</p>
                <p className="text-sm text-gray-600">
                  Available attribute types: {businessTypeConfig.types.map(type => ATTRIBUTE_TYPE_NAMES[type]).join(', ')}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Current Attributes */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Current Attributes</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.keys(attributesByType).length === 0 ? (
            <p className="text-gray-500 text-center py-8">
              No attributes selected. Add some attributes to help customers find your business.
            </p>
          ) : (
            Object.entries(attributesByType).map(([type, values]) => (
              <div key={type} className="space-y-2">
                <h4 className="font-medium text-gray-900">{ATTRIBUTE_TYPE_NAMES[type] || type}</h4>
                <div className="flex flex-wrap gap-2">
                  {values.map((value) => (
                    <Badge key={value} variant="secondary" className="flex items-center gap-1">
                      {value}
                      <button
                        onClick={() => removeAttribute(type, value)}
                        className="ml-1 hover:text-red-600"
                      >
                        <Trash2 className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>

      {/* Add Attributes */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Add Attributes</CardTitle>
          <div className="flex gap-2">
            <Input
              placeholder="Search available attributes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.keys(filteredAvailableByType).length === 0 ? (
            <p className="text-gray-500 text-center py-8">
              {searchTerm ? 'No attributes match your search.' : 'No available attributes found.'}
            </p>
          ) : (
            Object.entries(filteredAvailableByType).map(([type, values]) => (
              <div key={type} className="space-y-2">
                <h4 className="font-medium text-gray-900">{ATTRIBUTE_TYPE_NAMES[type] || type}</h4>
                <div className="flex flex-wrap gap-2">
                  {values.map((value) => {
                    const isSelected = attributesByType[type]?.includes(value)
                    return (
                      <Button
                        key={value}
                        variant={isSelected ? "secondary" : "outline"}
                        size="sm"
                        disabled={isSelected}
                        onClick={() => addAttribute(type, value)}
                        className="h-8"
                      >
                        {isSelected ? (
                          <>
                            {value} ✓
                          </>
                        ) : (
                          <>
                            <Plus className="h-3 w-3 mr-1" />
                            {value}
                          </>
                        )}
                      </Button>
                    )
                  })}
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>
    </div>
  )
}
