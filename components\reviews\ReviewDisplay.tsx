"use client"

import { useState, useEffect } from 'react'
import { Star, MessageSquare, ThumbsUp, Calendar, User } from 'lucide-react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'

interface Review {
  id: number
  rating: number
  comment: string | null
  created_at: string
  order_id: number
  businesses?: {
    name: string
  }
  orders?: {
    order_number: string
  }
  user_name?: string
  business_reply?: {
    content: string
    created_at: string
  }
}

interface ReviewDisplayProps {
  businessId: number
  businessName: string
  showHeader?: boolean
  limit?: number
  className?: string
}

export function ReviewDisplay({ 
  businessId, 
  businessName, 
  showHeader = true, 
  limit = 10,
  className 
}: ReviewDisplayProps) {
  const [reviews, setReviews] = useState<Review[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [averageRating, setAverageRating] = useState<number>(0)
  const [totalReviews, setTotalReviews] = useState<number>(0)

  useEffect(() => {
    fetchReviews()
  }, [businessId])

  const fetchReviews = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/reviews?business_id=${businessId}&limit=${limit}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch reviews')
      }

      const data = await response.json()
      
      if (data.success) {
        setReviews(data.reviews || [])
        
        // Calculate average rating
        if (data.reviews && data.reviews.length > 0) {
          const avg = data.reviews.reduce((sum: number, review: Review) => sum + review.rating, 0) / data.reviews.length
          setAverageRating(Math.round(avg * 10) / 10)
          setTotalReviews(data.reviews.length)
        }
      }
    } catch (err) {
      console.error('Error fetching reviews:', err)
      setError(err instanceof Error ? err.message : 'Failed to load reviews')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  const renderStars = (rating: number, size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizeClasses = {
      sm: 'h-3 w-3',
      md: 'h-4 w-4',
      lg: 'h-5 w-5'
    }
    
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={cn(
              sizeClasses[size],
              star <= rating
                ? 'text-yellow-400 fill-yellow-400'
                : 'text-gray-300'
            )}
          />
        ))}
      </div>
    )
  }

  if (loading) {
    return (
      <div className={cn("space-y-4", className)}>
        {showHeader && (
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-48 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-32"></div>
          </div>
        )}
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-4">
                <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={cn("text-center py-8", className)}>
        <p className="text-red-600 mb-2">Failed to load reviews</p>
        <Button variant="outline" size="sm" onClick={fetchReviews}>
          Try Again
        </Button>
      </div>
    )
  }

  if (reviews.length === 0) {
    return (
      <div className={cn("text-center py-8", className)}>
        <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Reviews Yet</h3>
        <p className="text-gray-500">
          Be the first to leave a review for {businessName}!
        </p>
      </div>
    )
  }

  return (
    <div className={cn("space-y-4", className)}>
      {showHeader && (
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <div className="flex items-center">
              {renderStars(averageRating, 'lg')}
              <span className="text-2xl font-bold ml-2">{averageRating}</span>
            </div>
            <span className="text-gray-500">
              ({totalReviews} {totalReviews === 1 ? 'review' : 'reviews'})
            </span>
          </div>
          <p className="text-sm text-gray-600">
            Customer reviews for {businessName}
          </p>
        </div>
      )}

      <div className="space-y-3">
        {reviews.map((review) => (
          <Card key={review.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  {renderStars(review.rating, 'sm')}
                  <Badge variant="secondary" className="text-xs">
                    {review.rating}/5
                  </Badge>
                </div>
                <div className="flex items-center text-xs text-gray-500">
                  <Calendar className="h-3 w-3 mr-1" />
                  {formatDate(review.created_at)}
                </div>
              </div>

              {review.comment && (
                <div className="mb-3">
                  <p className="text-gray-700 leading-relaxed">
                    "{review.comment}"
                  </p>
                </div>
              )}

              <div className="flex items-center justify-between text-xs text-gray-500">
                <div className="flex items-center">
                  <User className="h-3 w-3 mr-1" />
                  <span>Customer</span>
                  {review.orders?.order_number && (
                    <>
                      <span className="mx-1">•</span>
                      <span>Order #{review.orders.order_number}</span>
                    </>
                  )}
                </div>
              </div>

              {review.business_reply && (
                <>
                  <Separator className="my-3" />
                  <div className="bg-gray-50 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center text-xs font-medium text-gray-700">
                        <MessageSquare className="h-3 w-3 mr-1" />
                        Response from {businessName}
                      </div>
                      <span className="text-xs text-gray-500">
                        {formatDate(review.business_reply.created_at)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-700">
                      {review.business_reply.content}
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {reviews.length >= limit && (
        <div className="text-center pt-4">
          <Button variant="outline" size="sm">
            Load More Reviews
          </Button>
        </div>
      )}
    </div>
  )
}
