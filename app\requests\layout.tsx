"use client"

import { usePathname } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Building2, Lightbulb, MapPin, Package, Settings } from "lucide-react"

interface RequestsLayoutProps {
  children: React.ReactNode
}

export default function RequestsLayout({ children }: RequestsLayoutProps) {
  const pathname = usePathname()

  // Don't show navigation on the main requests page
  const isMainRequestsPage = pathname === '/requests'

  if (isMainRequestsPage) {
    return <>{children}</>
  }

  const requestTypes = [
    {
      href: '/requests/businesses',
      label: 'Businesses',
      icon: Building2,
      description: 'Request new businesses to join Loop'
    },
    {
      href: '/requests/features',
      label: 'Features',
      icon: Lightbulb,
      description: 'Suggest new app features'
    },
    {
      href: '/requests/areas',
      label: 'Delivery Areas',
      icon: MapPin,
      description: 'Request delivery coverage in new areas'
    },
    {
      href: '/requests/products',
      label: 'Products',
      icon: Package,
      description: 'Request specific products from businesses'
    },
    {
      href: '/requests/services',
      label: 'Services',
      icon: Settings,
      description: 'Request new types of services'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <Link href="/requests" className="text-2xl font-bold text-gray-900 hover:text-blue-600">
                Requests
              </Link>
              <p className="text-sm text-gray-600 mt-1">Help shape Loop's future</p>
            </div>
            
            {/* Request Type Navigation */}
            <div className="hidden md:flex items-center space-x-1">
              {requestTypes.map((type) => {
                const Icon = type.icon
                const isActive = pathname === type.href
                
                return (
                  <Link key={type.href} href={type.href}>
                    <Button
                      variant={isActive ? "default" : "ghost"}
                      size="sm"
                      className={`flex items-center gap-2 ${
                        isActive 
                          ? 'bg-blue-600 text-white' 
                          : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
                      }`}
                    >
                      <Icon className="h-4 w-4" />
                      {type.label}
                    </Button>
                  </Link>
                )
              })}
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden mt-4">
            <div className="grid grid-cols-2 gap-2">
              {requestTypes.map((type) => {
                const Icon = type.icon
                const isActive = pathname === type.href
                
                return (
                  <Link key={type.href} href={type.href}>
                    <Card className={`cursor-pointer transition-colors ${
                      isActive ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:bg-gray-50'
                    }`}>
                      <CardContent className="p-3 text-center">
                        <Icon className={`h-5 w-5 mx-auto mb-1 ${
                          isActive ? 'text-blue-600' : 'text-gray-600'
                        }`} />
                        <div className={`text-sm font-medium ${
                          isActive ? 'text-blue-600' : 'text-gray-900'
                        }`}>
                          {type.label}
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                )
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Page Content */}
      {children}
    </div>
  )
}
