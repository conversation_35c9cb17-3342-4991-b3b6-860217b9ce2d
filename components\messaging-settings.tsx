'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import {
  MessageSquare,
  User,
  Shield,
  Clock,
  CheckCircle2,
  AlertCircle,
  Loader2
} from 'lucide-react'
import { MessagingSettings, DEFAULT_MESSAGING_SETTINGS } from '@/types/messaging-settings'

export function MessagingSettings() {
  const [settings, setSettings] = useState<MessagingSettings>(DEFAULT_MESSAGING_SETTINGS)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [usernameStatus, setUsernameStatus] = useState<'idle' | 'checking' | 'available' | 'taken' | 'invalid'>('idle')
  const { toast } = useToast()

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      const response = await fetch('/api/user/messaging-settings')
      if (response.ok) {
        const data = await response.json()
        setSettings(data.settings)
      } else {
        console.error('Failed to load messaging settings')
      }
    } catch (error) {
      console.error('Error loading messaging settings:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const checkUsername = async (username: string) => {
    if (!username || username.length < 3) {
      setUsernameStatus('idle')
      return
    }

    if (username.length > 30 || !/^[a-zA-Z0-9_-]+$/.test(username)) {
      setUsernameStatus('invalid')
      return
    }

    setUsernameStatus('checking')
    
    try {
      // For now, we'll validate on save. In a real implementation,
      // you might want a separate endpoint for username validation
      setUsernameStatus('available')
    } catch (error) {
      setUsernameStatus('taken')
    }
  }

  const handleUsernameChange = (value: string) => {
    setSettings(prev => ({ ...prev, public_username: value }))
    
    // Debounce username checking
    setTimeout(() => checkUsername(value), 500)
  }

  const handleSave = async () => {
    setIsSaving(true)
    
    try {
      const response = await fetch('/api/user/messaging-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      })

      const data = await response.json()

      if (response.ok) {
        toast({
          title: 'Settings saved',
          description: 'Your messaging preferences have been updated.',
        })
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to save settings',
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save settings. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSaving(false)
    }
  }

  const getUsernameIcon = () => {
    switch (usernameStatus) {
      case 'checking':
        return <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
      case 'available':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />
      case 'taken':
      case 'invalid':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  const getUsernameMessage = () => {
    switch (usernameStatus) {
      case 'checking':
        return 'Checking availability...'
      case 'available':
        return 'Username is available'
      case 'taken':
        return 'Username is already taken'
      case 'invalid':
        return 'Username can only contain letters, numbers, underscores, and hyphens'
      default:
        return 'Choose a unique username for messaging'
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-600">Loading messaging settings...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Messaging & Communication
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Core Messaging Toggle */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label className="text-base font-medium">Enable Messaging</Label>
            <p className="text-sm text-gray-600">
              Allow communication with businesses, drivers, and other customers
            </p>
          </div>
          <Switch
            checked={settings.messaging_enabled}
            onCheckedChange={(checked) =>
              setSettings(prev => ({ ...prev, messaging_enabled: checked }))
            }
          />
        </div>

        {settings.messaging_enabled && (
          <>
            <Separator />

            {/* Username Section */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <Label className="text-base font-medium">Public Username</Label>
                <Badge variant="secondary" className="text-xs">Optional</Badge>
              </div>
              <div className="space-y-2">
                <div className="relative">
                  <Input
                    placeholder="e.g., pizza_lover_123"
                    value={settings.public_username || ''}
                    onChange={(e) => handleUsernameChange(e.target.value)}
                    className="pr-10"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    {getUsernameIcon()}
                  </div>
                </div>
                <p className="text-xs text-gray-500">
                  {getUsernameMessage()}
                </p>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-sm font-medium">Discoverable by username</Label>
                  <p className="text-xs text-gray-600">
                    Allow others to find you by your username
                  </p>
                </div>
                <Switch
                  checked={settings.discoverable_by_username}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({ ...prev, discoverable_by_username: checked }))
                  }
                  disabled={!settings.public_username}
                />
              </div>
            </div>

            <Separator />

            {/* Communication Preferences */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                <Label className="text-base font-medium">Who can message you</Label>
              </div>
              
              <div className="space-y-3 ml-6">
                <div className="flex items-center justify-between">
                  <Label className="text-sm">Businesses</Label>
                  <Switch
                    checked={settings.allow_business_messages}
                    onCheckedChange={(checked) =>
                      setSettings(prev => ({ ...prev, allow_business_messages: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label className="text-sm">Drivers</Label>
                  <Switch
                    checked={settings.allow_driver_messages}
                    onCheckedChange={(checked) =>
                      setSettings(prev => ({ ...prev, allow_driver_messages: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label className="text-sm">Other customers</Label>
                  <Switch
                    checked={settings.allow_customer_messages}
                    onCheckedChange={(checked) =>
                      setSettings(prev => ({ ...prev, allow_customer_messages: checked }))
                    }
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Order-related Settings */}
            <div className="space-y-4">
              <Label className="text-base font-medium">Order Communication</Label>
              
              <div className="space-y-3 ml-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-sm">Auto-accept order conversations</Label>
                    <p className="text-xs text-gray-600">
                      Automatically start conversations for your orders
                    </p>
                  </div>
                  <Switch
                    checked={settings.auto_accept_order_conversations}
                    onCheckedChange={(checked) =>
                      setSettings(prev => ({ ...prev, auto_accept_order_conversations: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-sm">Allow order reviews</Label>
                    <p className="text-xs text-gray-600">
                      Let businesses request reviews via messages
                    </p>
                  </div>
                  <Switch
                    checked={settings.allow_order_reviews}
                    onCheckedChange={(checked) =>
                      setSettings(prev => ({ ...prev, allow_order_reviews: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-sm">Review reminders</Label>
                    <p className="text-xs text-gray-600">
                      Get reminded to leave reviews after orders
                    </p>
                  </div>
                  <Switch
                    checked={settings.review_reminders}
                    onCheckedChange={(checked) =>
                      setSettings(prev => ({ ...prev, review_reminders: checked }))
                    }
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Privacy Settings */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <Label className="text-base font-medium">Privacy & Data</Label>
              </div>
              
              <div className="space-y-3 ml-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-sm">Show real name</Label>
                    <p className="text-xs text-gray-600">
                      Display your real name instead of username
                    </p>
                  </div>
                  <Switch
                    checked={settings.show_real_name}
                    onCheckedChange={(checked) =>
                      setSettings(prev => ({ ...prev, show_real_name: checked }))
                    }
                  />
                </div>
                
                <div className="space-y-2">
                  <Label className="text-sm">Message history retention</Label>
                  <Select
                    value={settings.message_history_retention_days.toString()}
                    onValueChange={(value) =>
                      setSettings(prev => ({ 
                        ...prev, 
                        message_history_retention_days: parseInt(value) 
                      }))
                    }
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="30">30 days</SelectItem>
                      <SelectItem value="90">90 days</SelectItem>
                      <SelectItem value="365">1 year</SelectItem>
                      <SelectItem value="-1">Forever</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Save Button */}
        <div className="pt-4">
          <Button 
            onClick={handleSave} 
            disabled={isSaving}
            className="w-full bg-emerald-600 hover:bg-emerald-700"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Messaging Settings'
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
