"use client"

import type { ReactNode } from "react"
import { Inter } from "next/font/google"
import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import {
  BarChart3,
  Home,
  Settings,
  ShoppingBag,
  Users,
  ChevronDown,
  Bell,
  MenuIcon,
  Menu,
  LogOut,
  Package,
  Store,
  Pill,
  Coffee,
  Car,
  CheckSquare,
  Truck,
  UserCog,
  Building2,
  Upload,
  MessageSquare,
  ExternalLink,
  Tags,
  Search,
  LayoutGrid
} from "lucide-react"
import WheelLogoIcon from "@/components/wheel-logo-icon"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useAuth } from "@/context/unified-auth-context"
import PendingApprovalMessage from "@/components/business/pending-approval"
import { logToTerminal, logErrorToTerminal } from "@/lib/terminal-logger"
import { getAuthToken, storeAuthToken, clearAuthToken, addAuthHeaders, createDevToken } from "@/utils/auth-token"
import UserMenu from "@/components/auth/user-menu"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap"
})

interface BusinessData {
  id: number
  name: string
  slug?: string
  business_type_id: number
  business_type?: string
  logo_url?: string | null
}

interface BusinessOption {
  id: number
  name: string
  business_type?: string
}

interface BusinessAdminLayoutProps {
  children: ReactNode
}

export default function UnifiedBusinessAdminLayout({ children }: BusinessAdminLayoutProps) {
  const pathname = usePathname()
  const router = useRouter()
  const { user, userProfile, isBusinessManager, isBusinessStaff, isAdmin, isSuperAdmin, signOut, session, userRole, refreshUserProfile } = useAuth()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [businessData, setBusinessData] = useState<BusinessData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isPendingApproval, setIsPendingApproval] = useState(false)
  const [availableBusinesses, setAvailableBusinesses] = useState<BusinessOption[]>([])
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(null)

  // Load selected business ID from localStorage on initial load
  useEffect(() => {
    if (isAdminUser) {
      try {
        const storedBusinessId = localStorage.getItem('loop_jersey_selected_business_id')
        if (storedBusinessId) {
          console.log('Loading selected business ID from localStorage:', storedBusinessId)
          setSelectedBusinessId(parseInt(storedBusinessId))
        }
      } catch (e) {
        console.error('Error loading selected business ID from localStorage:', e)
      }
    }
  }, [isAdminUser])

  // Store selectedBusinessId in localStorage when it changes
  useEffect(() => {
    if (selectedBusinessId !== null) {
      localStorage.setItem('loop_jersey_selected_business_id', selectedBusinessId.toString());
      console.log("Stored selected business ID in localStorage:", selectedBusinessId);
    }
  }, [selectedBusinessId]);

  // Admin users can bypass business manager checks
  // TEMPORARILY FORCE ADMIN USER FOR DEVELOPMENT
  const isAdminUser = true // Temporarily force admin user to show business selector

  // TEMPORARILY DISABLED: Redirect if not logged in or not authorized
  useEffect(() => {
    const checkAuthAndRedirect = async () => {
      await logToTerminal("DISABLED: Starting business-admin access check - AUTH CHECKS DISABLED FOR DEVELOPMENT");
      return; // Early return to skip all auth checks

      // For super admin users, we can be more lenient about session requirements
      if (!user) {
        await logErrorToTerminal("No user found, redirecting to login");
        router.push("/login");
        return;
      }

      // If we have a user but no session, try to continue (especially for admin users)
      if (!session) {
        await logToTerminal("No session found, but user exists - checking if admin user");

        // For admin/super admin users, we can continue without a session if we have stored tokens
        if (isAdmin || isSuperAdmin) {
          await logToTerminal("Admin/Super admin user detected, continuing without session");
        } else {
          await logErrorToTerminal("No session and not admin user, redirecting to login");
          router.push("/login");
          return;
        }
      }

      await logToTerminal("User session found", {
        email: user.email,
        id: user.id,
        role: userRole,
        isBusinessManager,
        isBusinessStaff,
        isAdmin,
        isSuperAdmin
      });

      // Get the authentication token from localStorage
      const token = getAuthToken();

      if (!token) {
        await logErrorToTerminal("No authentication token found in localStorage");

        // In development mode, create a fake token
        if (process.env.NODE_ENV === 'development' && user) {
          await logToTerminal("Development mode: Creating fake token for user", {
            email: user.email
          });

          // Create a simple fake token for development using our utility function
          const fakeToken = createDevToken(user.email);

          await logToTerminal("Fake token stored in localStorage");

          // Continue with the fake token
          return;
        } else {
          // In production, redirect to login
          router.push("/login");
          return;
        }
      }

      await logToTerminal("Authentication token found in localStorage", {
        tokenLength: token.length,
        tokenStart: token.substring(0, 10) + '...'
      });

      // Skip token verification if we have a valid user profile with business-admin role
      if (userProfile && (isBusinessManager || isBusinessStaff || isAdmin || isSuperAdmin)) {
        await logToTerminal("Skipping token verification - user has valid business-admin role", {
          role: userProfile.role,
          email: user.email
        });
      } else {
        // Verify the token is valid by making a test API call
        try {
          await logToTerminal("Verifying token with API call to /api/auth/verify-token");

          const response = await fetch('/api/auth/verify-token', {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });

          if (!response.ok) {
            await logErrorToTerminal("Token verification failed", {
              status: response.status,
              statusText: response.statusText
            });

            // If we have a user profile with business-admin role, continue anyway
            if (userProfile && (isBusinessManager || isBusinessStaff || isAdmin || isSuperAdmin)) {
              await logToTerminal("Token verification failed but user has valid business-admin role, continuing");
            } else {
              // Try to get more details from the response
              try {
                const errorData = await response.json();
                await logErrorToTerminal("Token verification error details", errorData);
              } catch (parseErr) {
                await logErrorToTerminal("Could not parse error response", parseErr);
              }

              // In development mode, we can try to continue with the session data
              if (process.env.NODE_ENV === 'development' && user && session) {
                await logToTerminal("Development mode: Continuing with session data despite token verification failure");

                // Try to refresh the token from the session
                if (session.access_token) {
                  localStorage.setItem('loop_jersey_auth_token', session.access_token);
                  await logToTerminal("Updated token from session");
                }
              } else {
                // In production or if no session data, redirect to login
                router.push("/login");
                return;
              }
            }
          } else {
            const verifyResult = await response.json();
            await logToTerminal("Token verified successfully", verifyResult);
          }
        } catch (err) {
          await logErrorToTerminal("Error verifying token", err);

          // If we have a user profile with business-admin role, continue anyway
          if (userProfile && (isBusinessManager || isBusinessStaff || isAdmin || isSuperAdmin)) {
            await logToTerminal("Token verification error but user has valid business-admin role, continuing");
          } else {
            // In development mode, we can try to continue with the session data
            if (process.env.NODE_ENV === 'development' && user && session) {
              await logToTerminal("Development mode: Continuing with session data despite token verification error");

              // Try to refresh the token from the session
              if (session.access_token) {
                localStorage.setItem('loop_jersey_auth_token', session.access_token);
                await logToTerminal("Updated token from session");
              }
            } else {
              // In production or if no session data, redirect to login
              router.push("/login");
              return;
            }
          }
        }
      }

      // Check authorization - allow business_staff, business_manager, admin, and super_admin roles
      const isAuthorized = isBusinessManager || isBusinessStaff || isAdmin || isSuperAdmin;

      await logToTerminal("Authorization check", {
        isAuthorized,
        userRole,
        isBusinessManager,
        isBusinessStaff,
        isAdmin,
        isSuperAdmin,
        userEmail: user.email,
        userProfileFromContext: userProfile,
        sessionExists: !!session
      });

      if (!isAuthorized) {
        // Only allow business staff, business managers, admins, and super_admins
        await logErrorToTerminal("User not authorized for business admin area", {
          role: userRole,
          email: user.email,
          isBusinessManager,
          isBusinessStaff,
          isAdmin,
          isSuperAdmin
        });

        // In development mode, we can be more lenient
        if (process.env.NODE_ENV === 'development') {
          await logToTerminal("Development mode: Allowing access despite unauthorized role");
          // Don't redirect in development mode
        } else {
          // For unauthorized users in production, redirect to home
          await logToTerminal("Redirecting unauthorized user to home page");
          router.push("/");
        }
      } else {
        await logToTerminal("User authorized for business admin area", { role: userRole });
      }
    };

    checkAuthAndRedirect();
  }, [user, session, isBusinessManager, isBusinessStaff, isAdmin, isSuperAdmin, userRole, router])

  // Fetch available businesses for admin users
  useEffect(() => {
    const fetchAvailableBusinesses = async () => {
      if (!isAdminUser) return // Temporarily remove user check

      try {
        console.log("Fetching available businesses for admin user")

        // Get the authentication token from localStorage
        const token = localStorage.getItem('loop_jersey_auth_token') || ''

        const response = await fetch('/api/admin/businesses-direct', {
          headers: {
            'Authorization': token ? `Bearer ${token}` : '',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        })

        if (!response.ok) {
          console.error("Error fetching businesses:", response.status)
          return
        }

        const data = await response.json()
        console.log("Available businesses:", data.businesses)

        if (data.businesses && Array.isArray(data.businesses)) {
          const businessOptions = data.businesses.map((b: any) => ({
            id: b.id,
            name: b.name,
            business_type: b.business_type || b.business_types?.name || "Business"
          }))

          setAvailableBusinesses(businessOptions)

          // If no business is selected yet, select the first one
          if (businessOptions.length > 0 && !selectedBusinessId) {
            const firstBusinessId = businessOptions[0].id;
            setSelectedBusinessId(firstBusinessId);
            console.log("Auto-selecting first business:", businessOptions[0].name);

            // Store the selected business ID in localStorage for persistence
            try {
              localStorage.setItem('loop_jersey_selected_business_id', firstBusinessId.toString());
              console.log("Stored selected business ID in localStorage:", firstBusinessId);
            } catch (e) {
              console.error("Error storing selected business ID:", e);
            }
          }
        }
      } catch (err) {
        console.error("Error fetching available businesses:", err)
      }
    }

    if (isAdminUser) {
      fetchAvailableBusinesses()
    }
  }, [isAdminUser]) // Temporarily remove user dependency

  // Fetch business data
  useEffect(() => {
    const fetchBusinessData = async () => {

      if (!user) {
        await logToTerminal("Skipping business data fetch - no user");
        return;
      }

      setIsLoading(true);
      await logToTerminal("Starting business data fetch");

      try {
        await logToTerminal("Fetching business data from API", {
          userEmail: user.email,
          isAdminUser,
          selectedBusinessId
        });

        // Get the authentication token using our utility
        let token = getAuthToken();

        // If still no token, check if we have a session from Supabase
        if (!token && session?.access_token) {
          token = session.access_token;
          await logToTerminal("Token retrieved from Supabase session", {
            length: token.length,
            start: token.substring(0, 10) + '...'
          });

          // Store the token for future use
          storeAuthToken(token);
          await logToTerminal("Token stored in localStorage and cookie");
        }

        // In development mode, if we still don't have a token but have a user, create a fake token
        if (!token && process.env.NODE_ENV === 'development' && user) {
          await logToTerminal("Development mode: Creating fake token for user", {
            email: user.email
          });

          // Create a simple fake token for development using our utility function
          token = createDevToken(user.email);
        }

        if (!token) {
          await logErrorToTerminal("No authentication token found for business data fetch");

          // In development mode, we can try to continue without a token
          if (process.env.NODE_ENV === 'development') {
            await logToTerminal("Development mode: Continuing without token");
          } else {
            router.push('/login?redirectTo=' + encodeURIComponent(pathname || '/business-admin/dashboard'));
            return;
          }
        } else {
          await logToTerminal("Authentication token found for business data fetch", {
            tokenLength: token.length,
            tokenStart: token.substring(0, 10) + '...'
          });
        }

        // Build the URL based on whether we're an admin user with a selected business
        let url = '/api/business-admin/manager-data'

        // If admin user and a business is selected, add the business ID as a query parameter
        if (isAdminUser && selectedBusinessId) {
          url = `/api/business-admin/manager-data?businessId=${selectedBusinessId}`
          await logToTerminal(`Admin user fetching data for business ID: ${selectedBusinessId}`);
        }

        await logToTerminal("Making API request", { url });

        // Use the server API to get business data (bypasses RLS issues)
        const response = await fetch(url, {
          method: 'GET',
          headers: addAuthHeaders()
        });

        await logToTerminal("API response received", {
          status: response.status,
          statusText: response.statusText,
          ok: response.ok
        });

        if (!response.ok) {
          let errorData;
          try {
            // Try to parse the response as JSON
            const text = await response.text();

            // Check if the response is empty
            if (!text || text.trim() === '') {
              errorData = { error: `Empty response with status: ${response.status} ${response.statusText}` };
              await logErrorToTerminal("Empty error response", {
                status: response.status,
                statusText: response.statusText
              });
            } else {
              try {
                // Try to parse as JSON
                errorData = JSON.parse(text);
                await logErrorToTerminal("Error response data", errorData);
              } catch (jsonErr) {
                // If not valid JSON, use the text directly
                errorData = { error: text };
                await logErrorToTerminal("Non-JSON error response", {
                  text: text.substring(0, 500), // Limit length for logging
                  status: response.status
                });
              }
            }
          } catch (parseErr) {
            errorData = { error: response.statusText };
            await logErrorToTerminal("Could not parse error response", {
              parseError: String(parseErr),
              statusText: response.statusText
            });
          }

          // Handle specific error cases
          if (response.status === 401) {
            await logErrorToTerminal("Authentication required for business data");

            // Clear any invalid tokens
            clearAuthToken();
            await logToTerminal("Cleared invalid authentication tokens");

            // In development mode, we can try to continue with admin fallback
            if (process.env.NODE_ENV === 'development' && (isAdmin || isSuperAdmin)) {
              await logToTerminal("Development mode: Admin user detected, continuing with fallback data");

              // Skip the redirect and let the fallback code below handle it
              throw new Error("Using admin fallback in development mode");
            } else {
              // Redirect to login with the current path
              const redirectPath = encodeURIComponent(pathname || '/business-admin/dashboard');
              router.push(`/login?redirectTo=${redirectPath}`);
              return;
            }
          } else if (response.status === 403) {
            await logErrorToTerminal("Permission denied for business data", { error: errorData.error });
            router.push('/');
            return;
          } else if (response.status === 404) {
            await logErrorToTerminal("No business found", { error: errorData.error });

            // Check localStorage for registration success data
            const registrationData = localStorage.getItem('business_registration_success');
            if (registrationData) {
              await logToTerminal("Found registration success data in localStorage");
              setIsPendingApproval(true);
              return;
            }

            // If we're not already on the debug page, redirect there
            if (!pathname?.includes('/business-admin/debug')) {
              await logToTerminal("Redirecting to debug page");
              router.push('/business-admin/debug');
            }
            return;
          }

          throw new Error(errorData.error || `Failed to fetch business data: ${response.statusText}`);
        }

        const data = await response.json();
        await logToTerminal("Business data from API", data);

        // Handle different response types
        if (data.isAdminUser) {
          await logToTerminal("Admin user detected");
          setBusinessData(data.businessData);
        } else if (data.isPendingApproval) {
          await logToTerminal("Business is pending approval");
          setIsPendingApproval(true);
        } else if (data.businessData) {
          await logToTerminal("Business data found", data.businessData);
          setBusinessData(data.businessData);
        } else {
          await logErrorToTerminal("Unexpected response format", data);
          throw new Error("Unexpected response format");
        }
      } catch (err) {
        await logErrorToTerminal("Error fetching business data", err);

        // Fallback for admin users
        if (isAdminUser) {
          await logToTerminal("Admin user detected, using default business data");
          setBusinessData({
            id: 0,
            name: "Admin View",
            business_type_id: 1,
            business_type: "Admin",
            logo_url: null
          });
        }
      } finally {
        setIsLoading(false);
        await logToTerminal("Business data fetch complete");
      }
    };

    fetchBusinessData();
  }, [user, isAdminUser, pathname, router, selectedBusinessId]);

  const handleLogout = async () => {
    // Clear auth tokens before signing out
    clearAuthToken();
    await logToTerminal("Cleared authentication tokens during logout");

    // Sign out using the auth service
    await signOut();

    // Redirect to home page
    router.push("/");
  }

  // Get business type specific icon
  const getBusinessTypeIcon = () => {
    if (!businessData?.business_type) return <Store className="w-4 h-4 mr-2 text-gray-500" />

    switch (businessData.business_type.toLowerCase()) {
      case 'restaurant':
        return <MenuIcon className="w-4 h-4 mr-2 text-gray-500" />
      case 'shop':
        return <Package className="w-4 h-4 mr-2 text-gray-500" />
      case 'pharmacy':
        return <Pill className="w-4 h-4 mr-2 text-gray-500" />
      case 'cafe':
        return <Coffee className="w-4 h-4 mr-2 text-gray-500" />
      case 'lift':
        return <Car className="w-4 h-4 mr-2 text-gray-500" />
      case 'errand':
        return <CheckSquare className="w-4 h-4 mr-2 text-gray-500" />
      default:
        return <Store className="w-4 h-4 mr-2 text-gray-500" />
    }
  }

  // Get business type specific menu label
  const getBusinessTypeMenuLabel = () => {
    if (!businessData?.business_type) return "Products"

    switch (businessData.business_type.toLowerCase()) {
      case 'restaurant':
        return "Menu"
      case 'shop':
      case 'pharmacy':
        return "Products"
      default:
        return "Products"
    }
  }

  // Get business type specific menu path
  const getBusinessTypeMenuPath = () => {
    return "/business-admin/menu"
  }

  const isAuthorized = true; // TEMPORARILY DISABLED: Always authorized for development

  // Admin users bypass all approval checks
  if (isPendingApproval && !isAdminUser) {
    return <PendingApprovalMessage />
  }

  // Only show loading if we're explicitly loading
  // Don't block rendering if user is null - this prevents flashing when navigating
  // Also, don't check authorization here - let the middleware handle that
  if (isLoading) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>
  }

  return (
    <div className={`${inter.variable} font-sans flex min-h-screen bg-white business-admin-container`}>
      <style jsx global>{`
        /* Wheel logo animation */
        @keyframes spin {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }

        .wheel-logo svg {
          transition: transform 0.3s ease;
        }

        .group:hover .group-hover\\:animate-spin {
          animation: spin 3s cubic-bezier(0.3, 0, 0.2, 1);
        }
      `}</style>
      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Fixed Navigation Bar */}
        <div className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm">
          <div className="container-fluid">
            <div className="flex items-center justify-between py-3">
              {/* Mobile Menu Button */}
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                <MenuIcon className="h-6 w-6" />
              </Button>

              {/* Logo and Navigation Dropdown - Desktop */}
              <div className="hidden md:flex items-center">
                <Link href="/business-admin/dashboard" className="flex items-center mr-4 group">
                  <div className="flex items-center bg-emerald-600 rounded-lg px-4 py-2.5 border border-emerald-500 shadow-sm h-10">
                    <div className="wheel-logo mr-2 group-hover:animate-spin">
                      <WheelLogoIcon
                        size={24}
                        color="white"
                        className="text-white w-6 h-6"
                      />
                    </div>
                    <span className="text-lg font-bold text-white">Loop</span>
                  </div>
                </Link>

                {/* Business Name Display */}
                {businessData && businessData.name && businessData.name !== "Admin View" && (
                  <div className="flex items-center mr-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg px-4 py-2.5 border border-gray-200 shadow-sm h-10">
                    <div className="flex items-center">
                      {businessData.logo_url ? (
                        <img
                          src={businessData.logo_url}
                          alt={businessData.name}
                          className="w-7 h-7 rounded-full object-cover mr-3 border-2 border-white shadow-sm"
                        />
                      ) : (
                        <div className="w-7 h-7 rounded-full bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center mr-3 shadow-sm">
                          <span className="text-white text-xs font-bold">
                            {businessData.name.substring(0, 2).toUpperCase()}
                          </span>
                        </div>
                      )}
                      <div className="flex flex-col justify-center">
                        <span className="text-sm font-semibold text-gray-900 leading-tight">
                          {businessData.name}
                        </span>
                        <span className="text-xs text-gray-500 leading-tight">
                          {businessData.business_type || 'Business'}
                        </span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Navigation Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="flex items-center h-10 px-4">
                      <span className="mr-2 font-medium">
                        {pathname === "/business-admin/dashboard" ? "Dashboard" :
                         pathname === "/business-admin/orders" || pathname?.startsWith("/business-admin/orders/") ? "Orders" :
                         pathname === "/business-admin/menu" || pathname?.startsWith("/business-admin/menu/") ? "Categories" :
                         pathname === "/business-admin/products" ? "Products" :
                         pathname === "/business-admin/product-upload" ? "Product Upload" :
                         pathname === "/business-admin/analytics" ? "Analytics" :
                         pathname === "/business-admin/customers" || pathname?.startsWith("/business-admin/customers/") ? "Customers" :
                         pathname === "/business-admin/driver-requests" ? "Driver Requests" :
                         pathname === "/business-admin/categories" || pathname?.startsWith("/business-admin/categories/") ? "Categories" :
                         pathname === "/business-admin/settings" ? "Settings" :
                         pathname === "/business-admin/connections-hub" ? "Connections Hub" :
                         "Navigation"}
                      </span>
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-56 max-h-[80vh] overflow-y-auto">
                    <DropdownMenuLabel>Dashboard</DropdownMenuLabel>
                    <DropdownMenuSeparator />

                    <DropdownMenuItem asChild>
                      <Link
                        href="/business-admin/dashboard"
                        className={`flex items-center w-full ${
                          pathname === "/business-admin/dashboard" ? "bg-gray-100 font-medium" : ""
                        }`}
                      >
                        <Home className="w-4 h-4 mr-2 text-gray-500" />
                        Dashboard
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuSeparator />
                    <DropdownMenuLabel>Orders</DropdownMenuLabel>

                    <DropdownMenuItem asChild>
                      <Link
                        href="/business-admin/orders"
                        className={`flex items-center w-full ${
                          pathname === "/business-admin/orders" || pathname?.startsWith("/business-admin/orders/")
                            ? "bg-gray-100 font-medium"
                            : ""
                        }`}
                      >
                        <ShoppingBag className="w-4 h-4 mr-2 text-gray-500" />
                        Orders
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuSeparator />
                    <DropdownMenuLabel>Categories</DropdownMenuLabel>

                    <DropdownMenuItem asChild>
                      <Link
                        href="/business-admin/categories"
                        className={`flex items-center w-full ${
                          pathname === "/business-admin/categories" && !pathname?.startsWith("/business-admin/categories/")
                            ? "bg-gray-100 font-medium"
                            : ""
                        }`}
                      >
                        <Search className="w-4 h-4 mr-2 text-gray-500" />
                        Categories Hub
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem asChild>
                      <Link
                        href="/business-admin/categories/search"
                        className={`flex items-center w-full ${
                          pathname === "/business-admin/categories/search"
                            ? "bg-gray-100 font-medium"
                            : ""
                        }`}
                      >
                        <Search className="w-4 h-4 mr-2 text-gray-500" />
                        Search Categories
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem asChild>
                      <Link
                        href="/business-admin/menu"
                        className={`flex items-center w-full ${
                          pathname === "/business-admin/menu" || pathname?.startsWith("/business-admin/menu/")
                            ? "bg-gray-100 font-medium"
                            : ""
                        }`}
                      >
                        <Menu className="w-4 h-4 mr-2 text-gray-500" />
                        Business Page
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem asChild>
                      <Link
                        href="/business-admin/categories/layout"
                        className={`flex items-center w-full ${
                          pathname === "/business-admin/categories/layout"
                            ? "bg-gray-100 font-medium"
                            : ""
                        }`}
                      >
                        <LayoutGrid className="w-4 h-4 mr-2 text-gray-500" />
                        Layout
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuSeparator />
                    <DropdownMenuLabel>Product Management</DropdownMenuLabel>

                    <DropdownMenuItem asChild>
                      <Link
                        href="/business-admin/products"
                        className={`flex items-center w-full ${
                          pathname === "/business-admin/products" ? "bg-gray-100 font-medium" : ""
                        }`}
                      >
                        <Package className="w-4 h-4 mr-2 text-gray-500" />
                        Products
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuItem asChild>
                      <Link
                        href="/business-admin/product-upload"
                        className={`flex items-center w-full ${
                          pathname === "/business-admin/product-upload" ? "bg-gray-100 font-medium" : ""
                        }`}
                      >
                        <Upload className="w-4 h-4 mr-2 text-gray-500" />
                        Product Upload
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuSeparator />
                    <DropdownMenuLabel>Analytics</DropdownMenuLabel>

                    <DropdownMenuItem asChild>
                      <Link
                        href="/business-admin/analytics"
                        className={`flex items-center w-full ${
                          pathname === "/business-admin/analytics" ? "bg-gray-100 font-medium" : ""
                        }`}
                      >
                        <BarChart3 className="w-4 h-4 mr-2 text-gray-500" />
                        Analytics
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuSeparator />
                    <DropdownMenuLabel>Customer Management</DropdownMenuLabel>

                    <DropdownMenuItem asChild>
                      <Link
                        href="/business-admin/customers"
                        className={`flex items-center w-full ${
                          pathname === "/business-admin/customers" || pathname?.startsWith("/business-admin/customers/")
                            ? "bg-gray-100 font-medium"
                            : ""
                        }`}
                      >
                        <Users className="w-4 h-4 mr-2 text-gray-500" />
                        Customers
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuSeparator />
                    <DropdownMenuLabel>Delivery</DropdownMenuLabel>

                    <DropdownMenuItem asChild>
                      <Link
                        href="/business-admin/driver-requests"
                        className={`flex items-center w-full ${
                          pathname === "/business-admin/driver-requests" ? "bg-gray-100 font-medium" : ""
                        }`}
                      >
                        <Truck className="w-4 h-4 mr-2 text-gray-500" />
                        Driver Requests
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuSeparator />
                    <DropdownMenuLabel>Communication</DropdownMenuLabel>

                    <DropdownMenuItem asChild>
                      <Link
                        href="/business-admin/connections-hub"
                        className={`flex items-center w-full ${
                          pathname === "/business-admin/connections-hub" ? "bg-gray-100 font-medium" : ""
                        }`}
                      >
                        <MessageSquare className="w-4 h-4 mr-2 text-gray-500" />
                        Connections Hub
                      </Link>
                    </DropdownMenuItem>

                    <DropdownMenuSeparator />
                    <DropdownMenuLabel>Settings</DropdownMenuLabel>

                    <DropdownMenuItem asChild>
                      <Link
                        href="/business-admin/settings"
                        className={`flex items-center w-full ${
                          pathname === "/business-admin/settings" ? "bg-gray-100 font-medium" : ""
                        }`}
                      >
                        <Settings className="w-4 h-4 mr-2 text-gray-500" />
                        Settings
                      </Link>
                    </DropdownMenuItem>

                    {/* View Business Page Link - Desktop Dropdown */}
                    {businessData?.slug && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem asChild>
                          <Link
                            href={`/business/${businessData.slug}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center w-full"
                          >
                            <ExternalLink className="w-4 h-4 mr-2 text-gray-500" />
                            View Business Page
                          </Link>
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Mobile Logo and Business Name */}
              <div className="md:hidden flex items-center">
                <Link href="/business-admin/dashboard" className="flex items-center group">
                  <div className="flex items-center bg-emerald-600 rounded-lg px-3 py-2 border border-emerald-500 shadow-sm h-9">
                    <div className="wheel-logo mr-1.5 group-hover:animate-spin">
                      <WheelLogoIcon
                        size={20}
                        color="white"
                        className="text-white w-5 h-5"
                      />
                    </div>
                    <span className="text-base font-bold text-white">Loop</span>
                  </div>
                </Link>

                {/* Mobile Business Name */}
                {businessData && businessData.name && businessData.name !== "Admin View" && (
                  <div className="ml-2 max-w-[120px] truncate">
                    <span className="text-xs font-medium text-gray-700 bg-gray-100 px-2 py-1 rounded-full border border-gray-200">
                      {businessData.name}
                    </span>
                  </div>
                )}
              </div>

              {/* Business Selector for Admin Users */}
              {isAdminUser && availableBusinesses.length > 0 && (
                <div className="hidden md:flex items-center ml-4">
                  <div className="flex items-center bg-gray-50 rounded-lg border px-3 py-2 h-10 shadow-sm">
                    <Building2 className="h-4 w-4 text-gray-500 mr-2" />
                    <Select
                      value={selectedBusinessId?.toString() || ""}
                      onValueChange={(value) => {
                        const newBusinessId = parseInt(value);
                        console.log('Business selector changed to:', newBusinessId);
                        setSelectedBusinessId(newBusinessId);

                        // Dispatch custom event to notify other components
                        window.dispatchEvent(new CustomEvent('businessChanged', {
                          detail: { businessId: newBusinessId }
                        }));
                      }}
                    >
                      <SelectTrigger className="border-0 bg-transparent p-0 h-auto shadow-none focus:ring-0 font-medium">
                        <SelectValue placeholder="Select a business" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableBusinesses.map((business) => (
                          <SelectItem key={business.id} value={business.id.toString()}>
                            {business.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}

              <div className="flex items-center ml-auto space-x-2">
                {/* View Business Page Link */}
                {businessData?.slug && (
                  <Button
                    variant="outline"
                    className="h-10 px-4 font-medium"
                    asChild
                  >
                    <Link
                      href={`/business/${businessData.slug}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center"
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      <span className="hidden sm:inline">View Page</span>
                    </Link>
                  </Button>
                )}

                <Button variant="outline" className="h-10 px-4 font-medium">
                  <Bell className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Notifications</span>
                </Button>

                {/* Account Menu - Desktop */}
                <div className="hidden md:block">
                  <UserMenu />
                </div>

                {/* Mobile only - avatar */}
                <Avatar className="h-8 w-8 md:hidden">
                  <AvatarImage src={businessData?.logo_url || ""} alt={businessData?.name || "Business"} />
                  <AvatarFallback className="bg-emerald-100 text-emerald-800">
                    {businessData?.name?.substring(0, 2).toUpperCase() || "BM"}
                  </AvatarFallback>
                </Avatar>
              </div>
            </div>
          </div>
        </div>

        {/* Spacer to account for fixed navigation bar */}
        <div className="h-16"></div>

        {/* Additional spacer for mobile menu when open */}
        {isMobileMenuOpen && <div className="md:hidden h-[calc(100vh-3rem)]"></div>}

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden bg-white border-b shadow-md fixed top-12 left-0 right-0 z-40">
            <div className="container-fluid">
              {/* Mobile Business Header */}
              {businessData && businessData.name && businessData.name !== "Admin View" && (
                <div className="py-3 px-3 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-gray-100">
                  <div className="flex items-center">
                    {businessData.logo_url ? (
                      <img
                        src={businessData.logo_url}
                        alt={businessData.name}
                        className="w-10 h-10 rounded-full object-cover mr-3 border-2 border-white shadow-sm"
                      />
                    ) : (
                      <div className="w-10 h-10 rounded-full bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center mr-3 shadow-sm">
                        <span className="text-white text-sm font-bold">
                          {businessData.name.substring(0, 2).toUpperCase()}
                        </span>
                      </div>
                    )}
                    <div className="flex flex-col">
                      <span className="text-base font-semibold text-gray-900 leading-tight">
                        {businessData.name}
                      </span>
                      <span className="text-sm text-gray-500 leading-tight">
                        {businessData.business_type || 'Business'}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              <nav className="py-4 space-y-1">
                <Link
                  href="/business-admin/dashboard"
                  className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                    pathname === "/business-admin/dashboard" ? "bg-gray-100 font-medium" : ""
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Home className="w-5 h-5 mr-3 text-gray-500" />
                  Dashboard
                </Link>

                <Link
                  href="/business-admin/orders"
                  className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                    pathname === "/business-admin/orders" || pathname?.startsWith("/business-admin/orders/")
                      ? "bg-gray-100 font-medium"
                      : ""
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <ShoppingBag className="w-5 h-5 mr-3 text-gray-500" />
                  Orders
                </Link>

                {/* Categories Section */}
                <div className="px-3 py-2">
                  <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    Categories
                  </div>
                </div>

                <Link
                  href="/business-admin/categories"
                  className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                    pathname === "/business-admin/categories" && !pathname?.startsWith("/business-admin/categories/")
                      ? "bg-gray-100 font-medium"
                      : ""
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Search className="w-5 h-5 mr-3 text-gray-500" />
                  Categories Hub
                </Link>

                <Link
                  href="/business-admin/categories/search"
                  className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                    pathname === "/business-admin/categories/search"
                      ? "bg-gray-100 font-medium"
                      : ""
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Search className="w-5 h-5 mr-3 text-gray-500" />
                  Search Categories
                </Link>

                <Link
                  href="/business-admin/menu"
                  className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                    pathname === "/business-admin/menu" || pathname?.startsWith("/business-admin/menu/")
                      ? "bg-gray-100 font-medium"
                      : ""
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Menu className="w-5 h-5 mr-3 text-gray-500" />
                  Business Page
                </Link>

                <Link
                  href="/business-admin/categories/layout"
                  className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                    pathname === "/business-admin/categories/layout"
                      ? "bg-gray-100 font-medium"
                      : ""
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <LayoutGrid className="w-5 h-5 mr-3 text-gray-500" />
                  Layout
                </Link>

                {/* Product Management Section */}
                <div className="px-3 py-2">
                  <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    Product Management
                  </div>
                </div>

                <Link
                  href="/business-admin/products"
                  className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                    pathname === "/business-admin/products" ? "bg-gray-100 font-medium" : ""
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Package className="w-5 h-5 mr-3 text-gray-500" />
                  Products
                </Link>

                <Link
                  href="/business-admin/product-upload"
                  className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                    pathname === "/business-admin/product-upload" ? "bg-gray-100 font-medium" : ""
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Upload className="w-5 h-5 mr-3 text-gray-500" />
                  Product Upload
                </Link>

                {/* Analytics Section */}
                <div className="px-3 py-2">
                  <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    Analytics
                  </div>
                </div>

                <Link
                  href="/business-admin/analytics"
                  className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                    pathname === "/business-admin/analytics" ? "bg-gray-100 font-medium" : ""
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <BarChart3 className="w-5 h-5 mr-3 text-gray-500" />
                  Analytics
                </Link>

                {/* Customer Management Section */}
                <div className="px-3 py-2">
                  <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    Customer Management
                  </div>
                </div>

                <Link
                  href="/business-admin/customers"
                  className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                    pathname === "/business-admin/customers" || pathname?.startsWith("/business-admin/customers/")
                      ? "bg-gray-100 font-medium"
                      : ""
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Users className="w-5 h-5 mr-3 text-gray-500" />
                  Customers
                </Link>

                {/* Delivery Section */}
                <div className="px-3 py-2">
                  <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    Delivery
                  </div>
                </div>

                <Link
                  href="/business-admin/driver-requests"
                  className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                    pathname === "/business-admin/driver-requests" ? "bg-gray-100 font-medium" : ""
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Truck className="w-5 h-5 mr-3 text-gray-500" />
                  Driver Requests
                </Link>

                {/* Communication Section */}
                <div className="px-3 py-2">
                  <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    Communication
                  </div>
                </div>

                <Link
                  href="/business-admin/connections-hub"
                  className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                    pathname === "/business-admin/connections-hub" ? "bg-gray-100 font-medium" : ""
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <MessageSquare className="w-5 h-5 mr-3 text-gray-500" />
                  Connections Hub
                </Link>

                {/* Settings Section */}
                <div className="px-3 py-2">
                  <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    Settings
                  </div>
                </div>

                <Link
                  href="/business-admin/settings"
                  className={`flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100 ${
                    pathname === "/business-admin/settings" ? "bg-gray-100 font-medium" : ""
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Settings className="w-5 h-5 mr-3 text-gray-500" />
                  Settings
                </Link>

                {/* View Business Page Link - Mobile */}
                {businessData?.slug && (
                  <Link
                    href={`/business/${businessData.slug}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <ExternalLink className="w-5 h-5 mr-3 text-gray-500" />
                    View Business Page
                  </Link>
                )}

                <Button
                  variant="ghost"
                  className="w-full flex items-center justify-start px-3 py-2 text-gray-700 rounded-md hover:bg-gray-100"
                  onClick={handleLogout}
                >
                  <LogOut className="w-5 h-5 mr-3 text-gray-500" />
                  Log out
                </Button>
              </nav>
            </div>
          </div>
        )}

        {/* Page Content */}
        <main className="flex-1 overflow-x-hidden w-full">
          <div className="container-fluid py-6 max-w-full">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
