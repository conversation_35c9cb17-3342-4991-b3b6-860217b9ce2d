import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const userId = '43560081-d469-4d4e-85e2-457bda286397'
    
    console.log('Testing context API for user:', userId)
    
    // Test the same query as the context API
    const { data: recentCompletedOrders, error: ordersError } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_id,
        driver_id,
        status,
        created_at,
        total,
        businesses(id, name, logo_url)
      `)
      .eq('user_id', userId)
      .in('status', ['delivered', 'picked_up'])
      .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
      .order('created_at', { ascending: false })
      .limit(3)

    console.log('Orders query result:', { recentCompletedOrders, ordersError })

    if (ordersError) {
      return NextResponse.json({ error: ordersError }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      userId,
      recentCompletedOrders,
      count: recentCompletedOrders?.length || 0,
      sevenDaysAgo: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
    })

  } catch (error) {
    console.error('Context test error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
