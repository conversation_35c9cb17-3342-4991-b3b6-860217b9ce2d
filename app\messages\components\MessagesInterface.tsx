"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import {
  MessageSquare,
  Plus,
  Search,
  ArrowLeft,
  Filter,
  Clock,
  CheckCircle2,
  Settings,
  HelpCircle
} from "lucide-react"
import { ConversationList } from './ConversationList'
import { ChatInterface } from './ChatInterface'
import { NewConversationFlow } from './NewConversationFlow'
import { QuickActions } from './QuickActions'
import { MessagingHelpDialog } from './MessagingHelpDialog'
import { cn } from "@/lib/utils"
import Link from 'next/link'

interface User {
  id: string
  email?: string
  user_metadata?: any
}

interface Context {
  order_id?: string | null
  business_id?: string | null
  rider_id?: string | null
  connection_id?: string | null
  role?: 'customer' | 'business' | 'rider' | null
  channel?: string | null
  continue?: boolean
}

interface MessagesInterfaceProps {
  user: User
  context: Context
}

type ViewState = 'list' | 'chat' | 'new-conversation'

export function MessagesInterface({ user, context }: MessagesInterfaceProps) {
  const [viewState, setViewState] = useState<ViewState>('list')
  const [selectedConversation, setSelectedConversation] = useState<{
    threadId: string
    contactName: string
    contactType: 'business' | 'rider' | 'customer'
    channelType: string
  } | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [showFilters, setShowFilters] = useState(false)

  // Smart context detection
  useEffect(() => {
    if (context.continue && context.connection_id) {
      // Direct continuation of existing conversation
      setSelectedConversation(context.connection_id)
      setViewState('chat')
    } else if (context.order_id || context.business_id || context.rider_id) {
      // Context-aware new conversation
      setViewState('new-conversation')
    }
  }, [context])

  const handleBackToList = () => {
    setViewState('list')
    setSelectedConversation(null)
  }

  const handleStartNewConversation = () => {
    setViewState('new-conversation')
  }

  const handleConversationSelected = (conversation: {
    threadId: string
    contactName: string
    contactType: 'business' | 'rider' | 'customer'
    channelType: string
  }) => {
    setSelectedConversation(conversation)
    setViewState('chat')
  }

  const handleConversationStarted = (conversationId: string) => {
    // For new conversations, we'll need to fetch the conversation details
    // For now, set a basic conversation object
    setSelectedConversation({
      threadId: conversationId,
      contactName: 'New Contact',
      contactType: 'customer',
      channelType: 'general_networking'
    })
    setViewState('chat')
  }

  // Mobile-first header
  const renderHeader = () => (
    <div className="sticky top-0 z-50 bg-white border-b">
      <div className="flex items-center justify-between p-4">
        {viewState !== 'list' ? (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBackToList}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="hidden sm:inline">Back</span>
          </Button>
        ) : (
          <h1 className="text-xl font-bold">Messages</h1>
        )}

        {viewState === 'list' && (
          <div className="flex items-center gap-2">
            <MessagingHelpDialog />
            <Link href="/account/settings">
              <Button
                variant="ghost"
                size="sm"
                className="h-9 w-9 p-0 sm:w-auto sm:px-3"
              >
                <Settings className="h-4 w-4" />
                <span className="hidden sm:inline sm:ml-2">Settings</span>
              </Button>
            </Link>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="h-9 w-9 p-0 sm:w-auto sm:px-3"
            >
              <Filter className="h-4 w-4" />
              <span className="hidden sm:inline sm:ml-2">Filter</span>
            </Button>
            <Button
              size="sm"
              onClick={handleStartNewConversation}
              className="bg-emerald-600 hover:bg-emerald-700 h-9 w-9 p-0 sm:w-auto sm:px-3"
            >
              <Plus className="h-4 w-4" />
              <span className="hidden sm:inline sm:ml-2">New</span>
            </Button>
          </div>
        )}
      </div>

      {/* Search bar for list view */}
      {viewState === 'list' && (
        <div className="px-4 pb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      )}
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50">
      {renderHeader()}

      <div className="relative">
        {viewState === 'list' && (
          <div className="space-y-4 pt-4">
            {/* Quick Actions */}
            <QuickActions
              user={user}
              onActionSelected={handleStartNewConversation}
            />

            {/* Filters */}
            {showFilters && (
              <div className="px-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="outline">All</Badge>
                      <Badge variant="outline">Unread</Badge>
                      <Badge variant="outline">Urgent</Badge>
                      <Badge variant="outline">Orders</Badge>
                      <Badge variant="outline">Businesses</Badge>
                      <Badge variant="outline">Riders</Badge>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Conversation List */}
            <ConversationList
              user={user}
              searchQuery={searchQuery}
              onConversationSelected={handleConversationSelected}
            />
          </div>
        )}

        {viewState === 'chat' && selectedConversation && (
          <ChatInterface
            user={user}
            threadId={selectedConversation.threadId}
            contactName={selectedConversation.contactName}
            contactType={selectedConversation.contactType}
            channelType={selectedConversation.channelType}
            onBack={handleBackToList}
          />
        )}

        {viewState === 'new-conversation' && (
          <NewConversationFlow
            user={user}
            context={context}
            onConversationStarted={handleConversationStarted}
            onCancel={handleBackToList}
          />
        )}
      </div>
    </div>
  )
}
