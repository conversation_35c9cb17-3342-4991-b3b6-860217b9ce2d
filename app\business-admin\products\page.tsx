"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/context/unified-auth-context"
import { useBusinessData } from "@/hooks/use-business-data"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import {
  <PERSON>,
  Filter,
  Edit,
  Package,
  Star,
  Eye,
  EyeOff,
  RefreshCc<PERSON>,
  Plus,
  ExternalLink,
  HelpCircle
} from "lucide-react"
import Link from "next/link"

interface ProductVariant {
  id: number
  product_id: number
  name: string
  price_adjustment: number
  is_default: boolean
  is_available: boolean
  display_order: number
  created_at: string
  updated_at: string
}

interface CustomizationOption {
  id: number
  name: string
  slug: string
  price: number
  is_default: boolean
  display_order: number
}

interface CustomizationGroup {
  id: number
  name: string
  slug: string
  is_required: boolean
  is_multiple: boolean
  min_selections: number
  max_selections: number
  display_order: number
  customization_options: CustomizationOption[]
}

interface Product {
  id: number
  name: string
  description?: string
  price: number
  image_url?: string
  custom_category_id?: number
  is_available: boolean
  is_featured: boolean
  is_popular?: boolean
  slug?: string
  unit?: string
  quantity?: number
  created_at: string
  updated_at: string
  business_id: number
  business_custom_categories?: {
    id: number
    name: string
  }
  variants?: ProductVariant[]
  customization_groups?: CustomizationGroup[]
}

interface Category {
  id: number
  name: string
}

export default function BusinessAdminProducts() {
  const { user, userProfile, isAdmin, isSuperAdmin } = useAuth()
  const { business, selectedBusinessId, isAdminUser, availableBusinesses, handleBusinessChange } = useBusinessData()
  const { toast } = useToast()

  // Debug admin status
  useEffect(() => {
    console.log('🔍 Products Page - Auth Status:', {
      user: !!user,
      userEmail: user?.email,
      userProfile: !!userProfile,
      userRole: userProfile?.role,
      isAdmin,
      isSuperAdmin,
      isAdminUser,
      business: business?.name
    })
  }, [user, userProfile, isAdmin, isSuperAdmin, isAdminUser, business])

  // State management
  const [products, setProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isHelpDialogOpen, setIsHelpDialogOpen] = useState(false)



  // Fetch products and categories
  const fetchData = async () => {
    try {
      setIsLoading(true)

      // For admin users, require a business ID
      if (isAdminUser && !selectedBusinessId) {
        console.log("Admin user but no business selected, skipping fetch")
        setIsLoading(false)
        return
      }

      // Build URL with business ID for admin users
      let url = '/api/business-admin/products'
      if (isAdminUser && selectedBusinessId) {
        url += `?businessId=${selectedBusinessId}`
      }

      // For non-admin users, the API will get their business ID from authentication

      console.log('🔍 PRODUCTS FETCH DEBUG:', {
        isAdminUser,
        selectedBusinessId,
        url,
        business: business?.name
      })

      // Get the authentication token from localStorage (same as orders page)
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      console.log('🔑 Token info:', {
        hasToken: !!token,
        tokenLength: token?.length || 0,
        tokenStart: token?.substring(0, 20) + '...'
      })

      const response = await fetch(url, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      console.log('🔍 Products API response status:', response.status)
      if (!response.ok) {
        const errorText = await response.text()
        console.error('🚨 Products API error:', errorText)
        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()
      console.log('✅ Products API response data:', data)
      console.log('🔍 Products count:', data.products?.length || 0)
      console.log('🔍 Products debug info:', data.debug)
      setProducts(data.products || [])

      // Fetch categories
      let categoriesUrl = '/api/business-admin/categories'
      if (isAdminUser && selectedBusinessId) {
        categoriesUrl += `?businessId=${selectedBusinessId}`
      }

      const categoriesResponse = await fetch(categoriesUrl, {
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (categoriesResponse.ok) {
        const categoriesData = await categoriesResponse.json()
        setCategories(categoriesData.categories || [])
      }

    } catch (error) {
      console.error("Error fetching products:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load products"
      })
    } finally {
      setIsLoading(false)
    }
  }



  // Initial data loading
  useEffect(() => {
    console.log('Products page useEffect:', { user: !!user, isAdminUser, selectedBusinessId, business: !!business })
    if (user) {
      if (isAdminUser && !selectedBusinessId) {
        // Admin user but no business selected yet, wait for business selection
        console.log("Admin user but no business selected, waiting...")
        setIsLoading(false)
        return
      }
      // For non-admin users or admin users with selected business, fetch data
      fetchData()
    }
  }, [user, isAdminUser, selectedBusinessId, business])

  // Filter products based on search and filters
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (product.description || "").toLowerCase().includes(searchTerm.toLowerCase())

    const matchesCategory = selectedCategory === "all" ||
                           (product.business_custom_categories?.id.toString() === selectedCategory)

    const matchesStatus = statusFilter === "all" ||
                         (statusFilter === "available" && product.is_available) ||
                         (statusFilter === "unavailable" && !product.is_available) ||
                         (statusFilter === "featured" && product.is_featured)

    return matchesSearch && matchesCategory && matchesStatus
  })

  // Calculate statistics
  const stats = {
    total: products.length,
    available: products.filter(p => p.is_available).length,
    featured: products.filter(p => p.is_featured).length,
    unavailable: products.filter(p => !p.is_available).length,
    withVariants: products.filter(p => p.variants && p.variants.length > 0).length,
    withCustomizations: products.filter(p => p.customization_groups && p.customization_groups.length > 0).length
  }

  // Handle edit product
  const handleEditProduct = (product: Product) => {
    setEditingProduct(product)
    setIsEditDialogOpen(true)
  }

  // Handle save product
  const handleSaveProduct = async (updatedProduct: Partial<Product>) => {
    if (!editingProduct) return

    try {
      setIsSubmitting(true)

      // Get the authentication token from localStorage (same as other API calls)
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      const response = await fetch(`/api/business-admin/products/${editingProduct.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        body: JSON.stringify(updatedProduct)
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error('API error response:', errorData)
        throw new Error(errorData.error || `Failed to update product (${response.status})`)
      }

      const data = await response.json()
      console.log('Product update response:', data)

      // Update the product in the list
      setProducts(prev => prev.map(p =>
        p.id === editingProduct.id ? { ...p, ...updatedProduct } : p
      ))

      setIsEditDialogOpen(false)
      setEditingProduct(null)

      toast({
        title: "Success",
        description: "Product updated successfully"
      })

    } catch (error: any) {
      console.error("Error updating product:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to update product"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount)
  }

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Products</h1>
          <p className="text-gray-600">Review and manage your products</p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={fetchData}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCcw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Link href="/business-admin/menu">
            <Button className="bg-emerald-600 hover:bg-emerald-700 flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Product
            </Button>
          </Link>
          <Link href="/product-upload">
            <Button variant="outline" className="flex items-center gap-2">
              <ExternalLink className="h-4 w-4" />
              Bulk Upload
            </Button>
          </Link>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsHelpDialogOpen(true)}
            className="h-8 flex items-center gap-2"
          >
            <HelpCircle className="h-3.5 w-3.5" />
            Help
          </Button>
        </div>
      </div>

      {/* Admin Business Selection */}
      {isAdminUser && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Admin: Select Business</CardTitle>
            <CardDescription>Choose a business to view its products</CardDescription>
          </CardHeader>
          <CardContent>
            <Select
              value={selectedBusinessId?.toString() || "none"}
              onValueChange={(value) => {
                const newBusinessId = value === "none" ? null : parseInt(value)
                console.log("🔄 Business selector changed to:", newBusinessId)
                if (newBusinessId) {
                  // Use the business data hook to handle the change
                  handleBusinessChange(newBusinessId)
                  // Also manually trigger a refetch of products
                  setTimeout(() => {
                    console.log("🔄 Triggering manual products refetch after business change")
                    fetchData()
                  }, 100)
                }
              }}
            >
              <SelectTrigger className="w-full max-w-md">
                <SelectValue placeholder="Select a business..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Select a business...</SelectItem>
                {availableBusinesses.map((business) => (
                  <SelectItem key={business.id} value={business.id.toString()}>
                    {business.name} ({business.business_type})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Products</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Available</p>
                <p className="text-2xl font-bold text-green-600">{stats.available}</p>
              </div>
              <Eye className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Featured</p>
                <p className="text-2xl font-bold text-orange-600">{stats.featured}</p>
              </div>
              <Star className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Unavailable</p>
                <p className="text-2xl font-bold text-red-600">{stats.unavailable}</p>
              </div>
              <EyeOff className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">With Variants</p>
                <p className="text-2xl font-bold text-purple-600">{stats.withVariants}</p>
              </div>
              <Package className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">With Options</p>
                <p className="text-2xl font-bold text-indigo-600">{stats.withCustomizations}</p>
              </div>
              <Filter className="h-8 w-8 text-indigo-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id.toString()}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="available">Available</SelectItem>
                <SelectItem value="unavailable">Unavailable</SelectItem>
                <SelectItem value="featured">Featured</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card>
        <CardHeader>
          <CardTitle>Products ({filteredProducts.length})</CardTitle>
          <CardDescription>
            Click on any product to edit its details
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <div className="relative overflow-x-auto">
              <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">Image</TableHead>
                  <TableHead>Product Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead className="text-right">Price</TableHead>
                  <TableHead className="text-center">Variants</TableHead>
                  <TableHead className="text-center">Customizations</TableHead>
                  <TableHead className="text-center">Status</TableHead>
                  <TableHead className="text-center">Featured</TableHead>
                  <TableHead className="text-center">Updated</TableHead>
                  <TableHead className="text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={10} className="h-24 text-center">
                      <div className="flex items-center justify-center">
                        <RefreshCcw className="h-5 w-5 animate-spin text-gray-400" />
                        <span className="ml-2">Loading products...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredProducts.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={10} className="h-24 text-center">
                      <div className="flex flex-col items-center justify-center text-gray-500">
                        <Package className="h-8 w-8 mb-2" />
                        <p>No products found</p>
                        <p className="text-sm">Try adjusting your search or filters</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredProducts.map((product) => (
                    <TableRow key={product.id} className="hover:bg-gray-50">
                      <TableCell>
                        <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
                          {product.image_url ? (
                            <img
                              src={product.image_url}
                              alt={product.name}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = '/placeholder-food.jpg'
                              }}
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <Package className="h-6 w-6 text-gray-400" />
                            </div>
                          )}
                        </div>
                      </TableCell>

                      <TableCell>
                        <div>
                          <p className="font-medium">{product.name}</p>
                          {product.description && (
                            <p className="text-sm text-gray-500 truncate max-w-xs">
                              {product.description}
                            </p>
                          )}
                        </div>
                      </TableCell>

                      <TableCell>
                        {product.business_custom_categories?.name || (
                          <span className="text-gray-400">No category</span>
                        )}
                      </TableCell>

                      <TableCell className="text-right font-medium">
                        {formatCurrency(product.price)}
                      </TableCell>

                      <TableCell className="text-center">
                        {product.variants && product.variants.length > 0 ? (
                          <div className="flex flex-col gap-1">
                            <Badge variant="outline" className="text-xs">
                              {product.variants.length} variant{product.variants.length !== 1 ? 's' : ''}
                            </Badge>
                            <div className="text-xs text-gray-500">
                              {product.variants.slice(0, 2).map(variant => variant.name).join(', ')}
                              {product.variants.length > 2 && '...'}
                            </div>
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">None</span>
                        )}
                      </TableCell>

                      <TableCell className="text-center">
                        {product.customization_groups && product.customization_groups.length > 0 ? (
                          <div className="flex flex-col gap-1">
                            <Badge variant="outline" className="text-xs">
                              {product.customization_groups.length} group{product.customization_groups.length !== 1 ? 's' : ''}
                            </Badge>
                            <div className="text-xs text-gray-500">
                              {product.customization_groups.slice(0, 2).map(group => group.name).join(', ')}
                              {product.customization_groups.length > 2 && '...'}
                            </div>
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">None</span>
                        )}
                      </TableCell>

                      <TableCell className="text-center">
                        <Badge variant={product.is_available ? "default" : "secondary"}>
                          {product.is_available ? "Available" : "Unavailable"}
                        </Badge>
                      </TableCell>

                      <TableCell className="text-center">
                        {product.is_featured && (
                          <Badge variant="outline" className="text-orange-600 border-orange-600">
                            <Star className="h-3 w-3 mr-1" />
                            Featured
                          </Badge>
                        )}
                      </TableCell>

                      <TableCell className="text-center text-sm text-gray-500">
                        {formatDate(product.updated_at)}
                      </TableCell>

                      <TableCell className="text-center">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditProduct(product)}
                          className="flex items-center gap-1"
                        >
                          <Edit className="h-3 w-3" />
                          Edit
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Edit Product Dialog */}
      <EditProductDialog
        product={editingProduct}
        categories={categories}
        isOpen={isEditDialogOpen}
        onClose={() => {
          setIsEditDialogOpen(false)
          setEditingProduct(null)
        }}
        onSave={handleSaveProduct}
        isSubmitting={isSubmitting}
      />

      {/* Help Dialog */}
      <Dialog open={isHelpDialogOpen} onOpenChange={setIsHelpDialogOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5 text-emerald-600" />
              Products Management Help
            </DialogTitle>
            <DialogDescription>
              Learn how to effectively manage your product catalog
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6 max-h-[60vh] overflow-y-auto">
            {/* Product Management Overview */}
            <div>
              <h4 className="font-medium text-sm mb-3 text-emerald-900">Managing Your Products</h4>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-start gap-2">
                  <span className="h-2 w-2 rounded-full bg-emerald-500 mt-2 flex-shrink-0"></span>
                  <span><strong>Add Products:</strong> Click "Add Product" to create new menu items with full customization options</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="h-2 w-2 rounded-full bg-emerald-500 mt-2 flex-shrink-0"></span>
                  <span><strong>Edit Products:</strong> Click "Edit" on any product to modify details, pricing, and availability</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="h-2 w-2 rounded-full bg-emerald-500 mt-2 flex-shrink-0"></span>
                  <span><strong>Bulk Upload:</strong> Use the "Bulk Upload" feature to add multiple products at once</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="h-2 w-2 rounded-full bg-emerald-500 mt-2 flex-shrink-0"></span>
                  <span><strong>Search & Filter:</strong> Use the search bar and filters to quickly find specific products</span>
                </div>
              </div>
            </div>

            {/* Product Status Management */}
            <div>
              <h4 className="font-medium text-sm mb-3 text-emerald-900">Product Status & Visibility</h4>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <Eye className="h-4 w-4 text-green-600" />
                  <span><strong>Available:</strong> Products customers can see and order</span>
                </div>
                <div className="flex items-center gap-2">
                  <EyeOff className="h-4 w-4 text-red-600" />
                  <span><strong>Unavailable:</strong> Hidden from customers (out of stock, seasonal items)</span>
                </div>
                <div className="flex items-center gap-2">
                  <Star className="h-4 w-4 text-orange-600" />
                  <span><strong>Featured:</strong> Highlighted products that appear prominently to customers</span>
                </div>
              </div>
            </div>

            {/* Product Organization */}
            <div>
              <h4 className="font-medium text-sm mb-3 text-emerald-900">Organizing Your Menu</h4>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• <strong>Categories:</strong> Group similar products together (e.g., "Mains", "Desserts", "Drinks")</li>
                <li>• <strong>Variants:</strong> Offer size or style options (e.g., "Small", "Medium", "Large")</li>
                <li>• <strong>Customizations:</strong> Let customers personalize orders (e.g., "Extra cheese", "No onions")</li>
                <li>• <strong>Pricing:</strong> Set competitive prices and use variants for different sizes</li>
              </ul>
            </div>

            {/* Best Practices */}
            <div className="bg-emerald-50 p-4 rounded-lg">
              <h4 className="font-medium text-sm mb-2 text-emerald-900">Best Practices</h4>
              <ul className="text-sm text-emerald-800 space-y-1">
                <li>• Keep product names clear and descriptive</li>
                <li>• Add high-quality images to increase customer appeal</li>
                <li>• Update availability regularly to avoid disappointing customers</li>
                <li>• Use featured status for your most popular or profitable items</li>
                <li>• Review and update prices regularly based on costs and demand</li>
                <li>• Organize products into logical categories for easy browsing</li>
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setIsHelpDialogOpen(false)}>
              Got it, thanks!
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Edit Product Dialog Component
interface EditProductDialogProps {
  product: Product | null
  categories: Category[]
  isOpen: boolean
  onClose: () => void
  onSave: (product: Partial<Product>) => void
  isSubmitting: boolean
}

function EditProductDialog({
  product,
  categories,
  isOpen,
  onClose,
  onSave,
  isSubmitting
}: EditProductDialogProps) {
  const [formData, setFormData] = useState<Partial<Product>>({})

  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name,
        description: product.description || "",
        price: product.price,
        custom_category_id: product.custom_category_id,
        is_available: product.is_available,
        is_featured: product.is_featured,
        unit: product.unit || "",
        quantity: product.quantity || 0
      })
    }
  }, [product])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave(formData)
  }

  if (!product) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Product</DialogTitle>
          <DialogDescription>
            Make changes to your product details
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Product Name</Label>
              <Input
                id="name"
                value={formData.name || ""}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="price">Price (£)</Label>
              <Input
                id="price"
                type="number"
                step="0.01"
                min="0"
                value={formData.price || ""}
                onChange={(e) => setFormData(prev => ({ ...prev, price: parseFloat(e.target.value) }))}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description || ""}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select
                value={formData.custom_category_id?.toString() || "none"}
                onValueChange={(value) => setFormData(prev => ({
                  ...prev,
                  custom_category_id: value === "none" ? undefined : parseInt(value)
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No category</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="unit">Unit</Label>
              <Input
                id="unit"
                value={formData.unit || ""}
                onChange={(e) => setFormData(prev => ({ ...prev, unit: e.target.value }))}
                placeholder="e.g., kg, piece, liter"
              />
            </div>
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Switch
                  id="available"
                  checked={formData.is_available || false}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_available: checked }))}
                />
                <Label htmlFor="available">Product Available</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="featured"
                  checked={formData.is_featured || false}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_featured: checked }))}
                />
                <Label htmlFor="featured">Featured Product</Label>
              </div>
            </div>
          </div>

          {/* Variants and Customizations Info */}
          {(product.variants?.length > 0 || product.customization_groups?.length > 0) && (
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-3">Additional Product Options</h4>

              {product.variants && product.variants.length > 0 && (
                <div className="mb-3">
                  <Label className="text-sm font-medium text-blue-800">Variants ({product.variants.length})</Label>
                  <div className="mt-1 space-y-1">
                    {product.variants.map((variant, index) => (
                      <div key={variant.id} className="flex justify-between items-center text-sm">
                        <span className="text-blue-700">
                          {variant.name} {variant.is_default && "(Default)"}
                        </span>
                        <span className="text-blue-600">
                          {variant.price_adjustment > 0 ? '+' : ''}{formatCurrency(variant.price_adjustment)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {product.customization_groups && product.customization_groups.length > 0 && (
                <div>
                  <Label className="text-sm font-medium text-blue-800">Customization Groups ({product.customization_groups.length})</Label>
                  <div className="mt-1 space-y-2">
                    {product.customization_groups.map((group, index) => (
                      <div key={group.id} className="text-sm">
                        <div className="flex justify-between items-center">
                          <span className="text-blue-700 font-medium">
                            {group.name} {group.is_required && "(Required)"}
                          </span>
                          <span className="text-blue-600 text-xs">
                            {group.customization_options?.length || 0} options
                          </span>
                        </div>
                        {group.customization_options && group.customization_options.length > 0 && (
                          <div className="ml-3 mt-1 space-y-1">
                            {group.customization_options.slice(0, 3).map((option) => (
                              <div key={option.id} className="flex justify-between items-center text-xs text-blue-600">
                                <span>{option.name}</span>
                                <span>{option.price > 0 ? `+${formatCurrency(option.price)}` : 'Free'}</span>
                              </div>
                            ))}
                            {group.customization_options.length > 3 && (
                              <div className="text-xs text-blue-500">
                                +{group.customization_options.length - 3} more options...
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="mt-3 text-xs text-blue-600">
                To manage variants and customizations, use the full product editor.
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
