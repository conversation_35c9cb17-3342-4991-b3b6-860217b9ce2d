import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { verifyBusinessAdminAccess } from "@/lib/simple-auth"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: Request) {
  try {
    console.log("Categories API called")

    // Use the same auth pattern as super-admin routes
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    // Parse URL to get query parameters
    const url = new URL(request.url)
    const businessIdParam = url.searchParams.get('businessId')

    const userProfile = accessCheck.profile
    const isAdmin = userProfile.role === "admin" || userProfile.role === "super_admin"

    // Determine which business to fetch categories for
    let businessId: number

    if (businessIdParam) {
      // Admin user requesting specific business
      if (!isAdmin) {
        return NextResponse.json(
          { error: "Only admins can specify business ID" },
          { status: 403 }
        )
      }
      businessId = parseInt(businessIdParam)
    } else {
      // Regular user - use their associated business
      if (!isAdmin && !userProfile.business_id) {
        return NextResponse.json(
          { error: "No business associated with this account" },
          { status: 403 }
        )
      }
      
      // For admin users without a business_id, we need a business ID parameter
      if (isAdmin && !userProfile.business_id) {
        return NextResponse.json(
          { error: "Admin users must specify a business ID parameter" },
          { status: 400 }
        )
      }
      
      businessId = userProfile.business_id
    }

    console.log("Fetching categories for business ID:", businessId)

    // Fetch business custom categories
    const { data: categories, error: categoriesError } = await adminClient
      .from("business_custom_categories")
      .select(`
        id,
        name,
        description,
        display_order,
        business_id,
        created_at,
        updated_at
      `)
      .eq("business_id", businessId)
      .order("display_order", { ascending: true })
      .order("name", { ascending: true })

    if (categoriesError) {
      console.error("Error fetching categories:", categoriesError)
      
      // Return default categories for development
      return NextResponse.json({
        categories: [
          {
            id: 1,
            name: "Starters",
            description: "Appetizers and small plates",
            display_order: 1,
            business_id: businessId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            product_count: 0
          },
          {
            id: 2,
            name: "Main Courses",
            description: "Main dishes and entrees",
            display_order: 2,
            business_id: businessId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            product_count: 0
          },
          {
            id: 3,
            name: "Desserts",
            description: "Sweet treats and desserts",
            display_order: 3,
            business_id: businessId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            product_count: 0
          }
        ]
      })
    }

    // Get product counts for each category
    const categoryIds = categories.map(cat => cat.id)
    let productCounts: Record<number, number> = {}

    if (categoryIds.length > 0) {
      const { data: products } = await adminClient
        .from("products")
        .select("custom_category_id")
        .eq("business_id", businessId)
        .in("custom_category_id", categoryIds)

      if (products) {
        productCounts = products.reduce((acc, product) => {
          const catId = product.custom_category_id
          acc[catId] = (acc[catId] || 0) + 1
          return acc
        }, {} as Record<number, number>)
      }
    }

    // Add product counts to categories
    const categoriesWithCounts = categories.map(category => ({
      ...category,
      product_count: productCounts[category.id] || 0
    }))

    console.log("Successfully fetched", categoriesWithCounts.length, "categories")

    return NextResponse.json({
      categories: categoriesWithCounts
    })

  } catch (error) {
    console.error("Categories API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    console.log("Creating new category")

    // Use the same auth pattern as super-admin routes
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const userProfile = accessCheck.profile
    const isAdmin = userProfile.role === "admin" || userProfile.role === "super_admin"

    // Parse the request body
    const requestData = await request.json()
    const { name, description, display_order, business_id } = requestData

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: "Category name is required" },
        { status: 400 }
      )
    }

    // Determine business ID
    let targetBusinessId = business_id

    if (!targetBusinessId) {
      if (!isAdmin && !userProfile.business_id) {
        return NextResponse.json(
          { error: "No business associated with this account" },
          { status: 403 }
        )
      }
      targetBusinessId = userProfile.business_id
    }

    // Create the category
    const { data: category, error: categoryError } = await adminClient
      .from("business_custom_categories")
      .insert({
        name,
        description,
        display_order: display_order || 0,
        business_id: targetBusinessId
      })
      .select()
      .single()

    if (categoryError) {
      console.error("Error creating category:", categoryError)
      return NextResponse.json(
        { error: "Failed to create category" },
        { status: 500 }
      )
    }

    console.log("Successfully created category:", category.name)

    return NextResponse.json({
      category: {
        ...category,
        product_count: 0
      }
    })

  } catch (error) {
    console.error("Create category API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
