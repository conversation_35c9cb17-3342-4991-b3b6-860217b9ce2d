import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Context API: Starting request processing - LATEST VERSION')
    console.log('🔄 Context API: Request URL:', request.url)
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    const orderId = searchParams.get('order_id')
    const businessId = searchParams.get('business_id')
    const riderId = searchParams.get('rider_id')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Generate a valid UUID if the provided user_id is not a UUID
    let validUserId = userId
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(userId)) {
      // Create a deterministic UUID based on the input string
      const crypto = require('crypto')
      const hash = crypto.createHash('md5').update(userId).digest('hex')
      validUserId = `${hash.slice(0,8)}-${hash.slice(8,12)}-${hash.slice(12,16)}-${hash.slice(16,20)}-${hash.slice(20,32)}`
    }

    const contextData: any = {
      user_id: userId,
      user_role: 'customer', // Default
      has_active_orders: false,
      recent_conversations: false
    }

    // Get user role from profile
    const { data: profile } = await supabase
      .from('connection_profiles')
      .select('profile_type, display_name')
      .eq('user_id', validUserId)
      .single()

    if (profile) {
      contextData.user_role = profile.profile_type || 'customer'
      contextData.display_name = profile.display_name
    }

    // Check for active orders
    const { data: activeOrders } = await supabase
      .from('orders')
      .select('id, order_number, business_id, status')
      .eq('user_id', validUserId)
      .in('status', ['confirmed', 'preparing', 'ready', 'out_for_delivery'])
      .limit(5)

    if (activeOrders && activeOrders.length > 0) {
      contextData.has_active_orders = true
      contextData.active_orders = activeOrders
    }

    // Check for recent completed orders (for review prompts)
    const { data: recentCompletedOrders, error: ordersError } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_id,
        driver_id,
        status,
        created_at,
        total,
        businesses(id, name, logo_url)
      `)
      .eq('user_id', validUserId)
      .in('status', ['delivered', 'picked_up'])
      .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()) // Last 7 days
      .order('created_at', { ascending: false })
      .limit(3)

    if (ordersError) {
      console.error('Error fetching recent completed orders:', ordersError)
    }


    if (recentCompletedOrders && recentCompletedOrders.length > 0) {
      contextData.has_recent_completed_orders = true
      contextData.recent_completed_orders = recentCompletedOrders
      console.log('✅ Context API: Found recent completed orders for user', validUserId, ':', recentCompletedOrders.length, 'orders')
      console.log('✅ Context API: Recent orders data:', JSON.stringify(recentCompletedOrders, null, 2))
    } else {
      console.log('❌ Context API: No recent completed orders found for user', validUserId)
    }

    // Check for recent business interactions (for business review prompts)
    const { data: recentBusinessInteractions } = await supabase
      .from('communications')
      .select(`
        id,
        business_id,
        created_at,
        businesses!inner(name)
      `)
      .eq('user_id', validUserId)
      .not('business_id', 'is', null)
      .gte('created_at', new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString()) // Last 14 days
      .order('created_at', { ascending: false })
      .limit(3)

    if (recentBusinessInteractions && recentBusinessInteractions.length > 0) {
      contextData.has_recent_business_interactions = true
      contextData.recent_business_interactions = recentBusinessInteractions
    }

    // If specific order context requested
    if (orderId) {
      const { data: orderDetails } = await supabase
        .from('orders')
        .select(`
          id,
          order_number,
          status,
          business_id,
          rider_id,
          businesses!inner(name, display_name),
          connection_profiles!rider_id(display_name)
        `)
        .eq('id', orderId)
        .single()

      if (orderDetails) {
        contextData.order = {
          id: orderDetails.id,
          order_number: orderDetails.order_number,
          status: orderDetails.status,
          business_name: orderDetails.businesses?.display_name || orderDetails.businesses?.name,
          rider_name: orderDetails.connection_profiles?.display_name
        }
      }
    }

    // If specific business context requested
    if (businessId) {
      const { data: businessDetails } = await supabase
        .from('businesses')
        .select('id, name, display_name, business_type')
        .eq('id', businessId)
        .single()

      if (businessDetails) {
        contextData.business = {
          id: businessDetails.id,
          name: businessDetails.display_name || businessDetails.name,
          type: businessDetails.business_type
        }
      }
    }

    // If specific rider context requested
    if (riderId) {
      const { data: riderDetails } = await supabase
        .from('connection_profiles')
        .select('user_id, display_name, profile_type')
        .eq('user_id', riderId)
        .eq('profile_type', 'rider')
        .single()

      if (riderDetails) {
        contextData.rider = {
          id: riderDetails.user_id,
          name: riderDetails.display_name
        }
      }
    }

    // Check for recent conversations
    const { data: recentMessages } = await supabase
      .from('communications')
      .select('id, created_at, sender_id, recipient_id')
      .or(`sender_id.eq.${userId},recipient_id.eq.${userId}`)
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours
      .limit(1)

    if (recentMessages && recentMessages.length > 0) {
      contextData.recent_conversations = true
    }

    // Get user's messaging preferences
    const { data: messagingSettings } = await supabase
      .from('connection_profiles')
      .select('communication_preferences')
      .eq('user_id', validUserId)
      .single()

    if (messagingSettings?.communication_preferences?.messaging) {
      contextData.messaging_preferences = messagingSettings.communication_preferences.messaging
    }

    console.log('🚀 Context API: Final response for user', validUserId, ':', JSON.stringify(contextData, null, 2))

    return NextResponse.json({
      success: true,
      context: contextData
    })

  } catch (error: any) {
    console.error('Error fetching messaging context:', error)
    return NextResponse.json(
      { error: 'Failed to fetch messaging context' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { user_id, template_id, variables } = body

    if (!user_id || !template_id) {
      return NextResponse.json(
        { error: 'User ID and template ID are required' },
        { status: 400 }
      )
    }

    // Log template usage for analytics
    const { error: logError } = await supabase
      .from('message_template_usage')
      .insert({
        user_id,
        template_id,
        variables: variables || {},
        used_at: new Date().toISOString()
      })

    if (logError) {
      console.error('Error logging template usage:', logError)
      // Don't fail the request for logging errors
    }

    // Get template suggestions based on usage patterns
    const { data: suggestions } = await supabase
      .from('message_template_usage')
      .select('template_id, count(*)')
      .eq('user_id', user_id)
      .gte('used_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Last 30 days
      .limit(5)

    return NextResponse.json({
      success: true,
      message: 'Template usage logged',
      suggestions: suggestions || []
    })

  } catch (error: any) {
    console.error('Error logging template usage:', error)
    return NextResponse.json(
      { error: 'Failed to log template usage' },
      { status: 500 }
    )
  }
}
