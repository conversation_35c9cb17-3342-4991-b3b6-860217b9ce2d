import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { validateDriverOrderAssignment, syncDriverStatus } from '@/lib/driver-validation'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(
  request: NextRequest,
  { params }: { params: { orderNumber: string } }
) {
  try {
    const orderNumber = params.orderNumber

    if (!orderNumber) {
      return NextResponse.json(
        { error: "Order number is required" },
        { status: 400 }
      )
    }

    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    if (!driverProfile.is_verified || !driverProfile.is_active) {
      return NextResponse.json(
        { error: "Driver not verified or inactive" },
        { status: 403 }
      )
    }

    // Check if driver is on shift and not on delivery
    const { data: driverStatus } = await supabase
      .from('driver_status')
      .select('is_on_shift, is_on_delivery')
      .eq('driver_id', driverProfile.id)
      .single()

    if (!driverStatus?.is_on_shift) {
      return NextResponse.json(
        { error: "Driver must be on shift to accept deliveries" },
        { status: 409 }
      )
    }

    if (driverStatus.is_on_delivery) {
      return NextResponse.json(
        { error: "Driver is already on a delivery" },
        { status: 409 }
      )
    }

    // Check if order is still available (using order_number instead of id)
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select('id, status, driver_id, order_number, business_id')
      .eq('order_number', orderNumber)
      .single()

    if (orderError || !order) {
      return NextResponse.json(
        { error: "Order not found" },
        { status: 404 }
      )
    }

    if (order.status !== 'offered') {
      return NextResponse.json(
        { error: "Order is no longer available" },
        { status: 409 }
      )
    }

    if (order.driver_id) {
      return NextResponse.json(
        { error: "Order has already been assigned to another driver" },
        { status: 409 }
      )
    }

    // COMPREHENSIVE VALIDATION: Check all business rules and consistency
    const validation = await validateDriverOrderAssignment(driverProfile.id, order.id)

    if (!validation.isValid) {
      return NextResponse.json(
        {
          error: "Order assignment validation failed",
          details: validation.errors,
          warnings: validation.warnings
        },
        { status: 403 }
      )
    }

    // Log warnings if any
    if (validation.warnings.length > 0) {
      console.warn('Order assignment warnings:', validation.warnings)
    }

    // SINGLE SOURCE OF TRUTH: Update orders table first
    const { error: updateError } = await supabase
      .from('orders')
      .update({
        status: 'assigned',
        driver_id: driverProfile.id,
        updated_at: new Date().toISOString()
      })
      .eq('id', order.id)

    if (updateError) {
      console.error('Error accepting delivery:', updateError)
      return NextResponse.json(
        { error: "Failed to accept delivery" },
        { status: 500 }
      )
    }

    // Create or update driver business assignment (working relationship)
    const { error: assignmentError } = await supabase
      .from('driver_business_assignments')
      .upsert({
        driver_id: user.auth_id, // Use auth_id, not driver_profile.id
        business_id: order.business_id,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'driver_id,business_id'
      })

    if (assignmentError) {
      console.error('Error creating driver business assignment:', assignmentError)
      // Don't fail the request - this is supplementary data
    }

    // Sync driver status to match orders table (derived state)
    const syncResult = await syncDriverStatus(driverProfile.id)
    if (!syncResult.isValid) {
      console.error('Error syncing driver status:', syncResult.errors)
      // Don't fail the request - orders table is the source of truth
    } else if (syncResult.warnings.length > 0) {
      console.log('Driver status sync warnings:', syncResult.warnings)
    }

    // Log the acceptance in order status history
    const { error: historyError } = await supabase
      .from('order_status_history')
      .insert({
        order_id: order.id,
        status: 'assigned',
        changed_by: user.auth_id,
        changed_by_type: 'driver',
        notes: 'Order accepted by driver',
        created_at: new Date().toISOString()
      })

    if (historyError) {
      console.error('Error logging status history:', historyError)
      // Don't fail the request, just log the error
    }

    return NextResponse.json({
      success: true,
      message: "Delivery accepted successfully",
      orderId: order.id,
      orderNumber: order.order_number,
      status: 'assigned'
    })

  } catch (error) {
    console.error('Error in accept delivery API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
