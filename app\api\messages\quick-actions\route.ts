import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Generate a valid UUID if the provided user_id is not a UUID
    let validUserId = userId
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(userId)) {
      // Create a deterministic UUID based on the input string
      const crypto = require('crypto')
      const hash = crypto.createHash('md5').update(userId).digest('hex')
      validUserId = `${hash.slice(0,8)}-${hash.slice(8,12)}-${hash.slice(12,16)}-${hash.slice(16,20)}-${hash.slice(20,32)}`
    }

    const quickActions: any[] = []

    // Check for recent completed orders (for order review prompts)
    const { data: recentCompletedOrders } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_id,
        status,
        created_at,
        businesses!inner(name)
      `)
      .eq('user_id', validUserId)
      .eq('status', 'delivered')
      .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()) // Last 7 days
      .order('created_at', { ascending: false })
      .limit(3)

    if (recentCompletedOrders && recentCompletedOrders.length > 0) {
      // Add order review quick actions
      recentCompletedOrders.forEach(order => {
        quickActions.push({
          id: `review-order-${order.id}`,
          type: 'review_order',
          title: `Review ${order.businesses.name}`,
          description: `Share your experience with order ${order.order_number}`,
          icon: '⭐',
          action_data: {
            order_id: order.id,
            order_number: order.order_number,
            business_name: order.businesses.name,
            business_id: order.business_id
          }
        })
      })
    }

    // Check for recent business interactions (for business review prompts)
    const { data: recentBusinessInteractions } = await supabase
      .from('communications')
      .select(`
        id,
        business_id,
        created_at,
        businesses!inner(name)
      `)
      .eq('user_id', validUserId)
      .not('business_id', 'is', null)
      .gte('created_at', new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString()) // Last 14 days
      .order('created_at', { ascending: false })
      .limit(3)

    if (recentBusinessInteractions && recentBusinessInteractions.length > 0) {
      // Add business review quick actions (avoid duplicates from order reviews)
      const existingBusinessIds = new Set(
        quickActions
          .filter(action => action.type === 'review_order')
          .map(action => action.action_data.business_id)
      )

      recentBusinessInteractions.forEach(interaction => {
        if (!existingBusinessIds.has(interaction.business_id)) {
          quickActions.push({
            id: `review-business-${interaction.business_id}`,
            type: 'review_business',
            title: `Review ${interaction.businesses.name}`,
            description: `Share your experience with this business`,
            icon: '💬',
            action_data: {
              business_id: interaction.business_id,
              business_name: interaction.businesses.name
            }
          })
        }
      })
    }

    // Add general quick actions
    quickActions.push(
      {
        id: 'track-order',
        type: 'track_order',
        title: 'Track My Order',
        description: 'Check the status of your current orders',
        icon: '📦',
        action_data: {}
      },
      {
        id: 'find-restaurants',
        type: 'find_restaurants',
        title: 'Find Restaurants',
        description: 'Discover new places to order from',
        icon: '🍽️',
        action_data: {}
      },
      {
        id: 'order-history',
        type: 'order_history',
        title: 'Order History',
        description: 'View your past orders',
        icon: '📋',
        action_data: {}
      },
      {
        id: 'help-support',
        type: 'help_support',
        title: 'Help & Support',
        description: 'Get help with your orders or account',
        icon: '❓',
        action_data: {}
      }
    )

    return NextResponse.json({
      success: true,
      quick_actions: quickActions
    })

  } catch (error) {
    console.error('Error fetching quick actions:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
