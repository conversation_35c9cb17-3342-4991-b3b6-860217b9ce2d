import React, { useState } from 'react';
import Link from "next/link";
import { Facebook, Instagram, Twitter, ChevronUp, ChevronDown } from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

interface FooterSectionProps {
  title: string;
  children: React.ReactNode;
}

const FooterSection: React.FC<FooterSectionProps> = ({ title, children }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen} className="border-b border-gray-800 py-3">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{title}</h3>
        <CollapsibleTrigger asChild>
          <button className="text-gray-400 hover:text-white">
            {isOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
          </button>
        </CollapsibleTrigger>
      </div>
      <CollapsibleContent className="pt-3">
        {children}
      </CollapsibleContent>
    </Collapsible>
  );
};

export default function MobileFooter() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-white md:hidden">
      <div className="container-fluid px-4 py-6">
        {/* Logo and Social Links - Always visible */}
        <div className="mb-6">
          <h3 className="text-xl font-bold mb-3">Loop</h3>
          <p className="text-gray-400 mb-4">From burgers to errands — Jersey's fastest way to get things done.</p>
          <div className="flex space-x-4">
            <a href="#" className="text-gray-400 hover:text-white">
              <Facebook size={20} />
            </a>
            <a href="#" className="text-gray-400 hover:text-white">
              <Instagram size={20} />
            </a>
            <a href="#" className="text-gray-400 hover:text-white">
              <Twitter size={20} />
            </a>
          </div>
        </div>

        {/* Collapsible Sections */}
        <FooterSection title="Explore">
          <ul className="space-y-3">
            <li>
              <Link href="/about" className="text-gray-400 hover:text-white">
                About Us
              </Link>
            </li>
            <li>
              <Link href="/contact" className="text-gray-400 hover:text-white">
                Contact
              </Link>
            </li>
            <li>
              <Link href="/careers" className="text-gray-400 hover:text-white">
                Careers
              </Link>
            </li>
            <li>
              <Link href="/requests" className="text-gray-400 hover:text-white">
                Requests
              </Link>
            </li>
          </ul>
        </FooterSection>

        <FooterSection title="Legal">
          <ul className="space-y-3">
            <li>
              <Link href="/terms" className="text-gray-400 hover:text-white">
                Terms of Service
              </Link>
            </li>
            <li>
              <Link href="/privacy" className="text-gray-400 hover:text-white">
                Privacy Policy
              </Link>
            </li>
            <li>
              <Link href="/cookies" className="text-gray-400 hover:text-white">
                Cookie Policy
              </Link>
            </li>
            <li>
              <Link href="/faqs" className="text-gray-400 hover:text-white">
                FAQs
              </Link>
            </li>
          </ul>
        </FooterSection>

        <FooterSection title="Contact">
          <address className="not-italic text-gray-400">
            <p>Loop Ltd</p>
            <p>15 King Street</p>
            <p>St Helier, Jersey</p>
            <p>JE2 4WE</p>
            <p className="mt-2"><EMAIL></p>
            <p>+44 1534 123456</p>
          </address>
        </FooterSection>

        {/* Copyright - Always visible */}
        <div className="mt-6 pt-4 text-center text-gray-400 text-sm">
          <p>&copy; {currentYear} Loop. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}
