import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    const { order_id, business_id, rating, comment, review_type } = await request.json()

    // Validate required fields
    if (!order_id || !business_id || !rating) {
      return NextResponse.json(
        { error: 'Missing required fields: order_id, business_id, and rating are required' },
        { status: 400 }
      )
    }

    if (rating < 1 || rating > 5) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      )
    }

    // Get the user_id from the order
    const { data: orderData, error: orderError } = await supabase
      .from('orders')
      .select('user_id, business_id')
      .eq('id', order_id)
      .single()

    if (orderError || !orderData) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    // Verify the business_id matches
    if (orderData.business_id !== business_id) {
      return NextResponse.json(
        { error: 'Business ID does not match order' },
        { status: 400 }
      )
    }

    // Check if a review already exists for this order and user
    const { data: existingReview } = await supabase
      .from('reviews')
      .select('id')
      .eq('order_id', order_id)
      .eq('user_id', orderData.user_id)
      .single()

    if (existingReview) {
      return NextResponse.json(
        { error: 'A review already exists for this order' },
        { status: 409 }
      )
    }

    // Create the review
    const { data: review, error: reviewError } = await supabase
      .from('reviews')
      .insert({
        user_id: orderData.user_id,
        business_id: business_id,
        order_id: order_id,
        rating: rating,
        comment: comment || null
      })
      .select()
      .single()

    if (reviewError) {
      console.error('Error creating review:', reviewError)
      return NextResponse.json(
        { error: 'Failed to create review' },
        { status: 500 }
      )
    }

    // Update business rating statistics
    await updateBusinessRating(business_id)

    return NextResponse.json({
      success: true,
      review: review,
      message: 'Review submitted successfully'
    })

  } catch (error) {
    console.error('Error in reviews API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const businessId = searchParams.get('business_id')
    const orderId = searchParams.get('order_id')
    const userId = searchParams.get('user_id')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')

    let query = supabase
      .from('reviews')
      .select(`
        id,
        rating,
        comment,
        created_at,
        order_id,
        business_id,
        businesses!inner(name),
        orders!inner(order_number)
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (businessId) {
      query = query.eq('business_id', parseInt(businessId))
    }

    if (orderId) {
      query = query.eq('order_id', parseInt(orderId))
    }

    if (userId) {
      query = query.eq('user_id', userId)
    }

    const { data: reviews, error } = await query

    if (error) {
      console.error('Error fetching reviews:', error)
      return NextResponse.json(
        { error: 'Failed to fetch reviews' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      reviews: reviews || [],
      pagination: {
        limit,
        offset,
        total: reviews?.length || 0
      }
    })

  } catch (error) {
    console.error('Error in reviews GET API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function updateBusinessRating(businessId: number) {
  try {
    // Calculate new average rating
    const { data: ratingData } = await supabase
      .from('reviews')
      .select('rating')
      .eq('business_id', businessId)

    if (ratingData && ratingData.length > 0) {
      const totalRating = ratingData.reduce((sum, review) => sum + review.rating, 0)
      const averageRating = totalRating / ratingData.length
      const reviewCount = ratingData.length

      // Update business rating and review count
      await supabase
        .from('businesses')
        .update({
          rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
          review_count: reviewCount
        })
        .eq('id', businessId)
    }
  } catch (error) {
    console.error('Error updating business rating:', error)
    // Don't throw error as this is not critical for the review submission
  }
}
