import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    const { order_id, business_id, driver_id, rating, comment, review_type } = await request.json()

    // Validate required fields
    if (!order_id || !rating || !review_type) {
      return NextResponse.json(
        { error: 'Missing required fields: order_id, rating, and review_type are required' },
        { status: 400 }
      )
    }

    if (rating < 1 || rating > 5) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      )
    }

    if (!['business', 'driver'].includes(review_type)) {
      return NextResponse.json(
        { error: 'review_type must be either "business" or "driver"' },
        { status: 400 }
      )
    }

    // Get the order data including driver assignment
    const { data: orderData, error: orderError } = await supabase
      .from('orders')
      .select('user_id, business_id, driver_id')
      .eq('id', order_id)
      .single()

    if (orderError || !orderData) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    if (review_type === 'business') {
      // Business review validation
      if (!business_id || orderData.business_id !== business_id) {
        return NextResponse.json(
          { error: 'Business ID does not match order or is missing' },
          { status: 400 }
        )
      }

      // Check if a business review already exists for this order and user
      const { data: existingReview } = await supabase
        .from('reviews')
        .select('id')
        .eq('order_id', order_id)
        .eq('user_id', orderData.user_id)
        .single()

      if (existingReview) {
        return NextResponse.json(
          { error: 'A business review already exists for this order' },
          { status: 409 }
        )
      }

      // Create the business review
      const { data: review, error: reviewError } = await supabase
        .from('reviews')
        .insert({
          user_id: orderData.user_id,
          business_id: business_id,
          order_id: order_id,
          rating: rating,
          comment: comment || null
        })
        .select()
        .single()

      if (reviewError) {
        console.error('Error creating business review:', reviewError)
        return NextResponse.json(
          { error: 'Failed to create business review' },
          { status: 500 }
        )
      }

      // Update business rating statistics
      await updateBusinessRating(business_id)

      return NextResponse.json({
        success: true,
        review: review,
        review_type: 'business',
        message: 'Business review submitted successfully'
      })

    } else if (review_type === 'driver') {
      // Driver review validation
      const targetDriverId = driver_id || orderData.driver_id

      if (!targetDriverId) {
        return NextResponse.json(
          { error: 'No driver assigned to this order' },
          { status: 400 }
        )
      }

      // Check if a driver rating already exists for this order and user
      const { data: existingRating } = await supabase
        .from('driver_ratings')
        .select('id')
        .eq('order_id', order_id)
        .eq('user_id', orderData.user_id)
        .single()

      if (existingRating) {
        return NextResponse.json(
          { error: 'A driver rating already exists for this order' },
          { status: 409 }
        )
      }

      // Create the driver rating
      const { data: driverRating, error: ratingError } = await supabase
        .from('driver_ratings')
        .insert({
          driver_id: targetDriverId,
          order_id: order_id,
          user_id: orderData.user_id,
          rating: rating,
          comment: comment || null
        })
        .select()
        .single()

      if (ratingError) {
        console.error('Error creating driver rating:', ratingError)
        return NextResponse.json(
          { error: 'Failed to create driver rating' },
          { status: 500 }
        )
      }

      // Update driver rating statistics
      await updateDriverRating(targetDriverId)

      return NextResponse.json({
        success: true,
        rating: driverRating,
        review_type: 'driver',
        message: 'Driver rating submitted successfully'
      })
    }

  } catch (error) {
    console.error('Error in reviews API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const businessId = searchParams.get('business_id')
    const driverId = searchParams.get('driver_id')
    const orderId = searchParams.get('order_id')
    const userId = searchParams.get('user_id')
    const reviewType = searchParams.get('review_type') // 'business' or 'driver'
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')

    if (reviewType === 'driver' || driverId) {
      // Fetch driver ratings
      let driverQuery = supabase
        .from('driver_ratings')
        .select(`
          id,
          rating,
          comment,
          created_at,
          order_id,
          driver_id,
          orders!inner(order_number)
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (driverId) {
        driverQuery = driverQuery.eq('driver_id', driverId)
      }

      if (orderId) {
        driverQuery = driverQuery.eq('order_id', parseInt(orderId))
      }

      if (userId) {
        driverQuery = driverQuery.eq('user_id', userId)
      }

      const { data: driverRatings, error: driverError } = await driverQuery

      if (driverError) {
        console.error('Error fetching driver ratings:', driverError)
        return NextResponse.json(
          { error: 'Failed to fetch driver ratings' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        reviews: driverRatings || [],
        review_type: 'driver',
        pagination: {
          limit,
          offset,
          total: driverRatings?.length || 0
        }
      })

    } else {
      // Fetch business reviews (default)
      let businessQuery = supabase
        .from('reviews')
        .select(`
          id,
          rating,
          comment,
          created_at,
          order_id,
          business_id,
          businesses!inner(name),
          orders!inner(order_number)
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (businessId) {
        businessQuery = businessQuery.eq('business_id', parseInt(businessId))
      }

      if (orderId) {
        businessQuery = businessQuery.eq('order_id', parseInt(orderId))
      }

      if (userId) {
        businessQuery = businessQuery.eq('user_id', userId)
      }

      const { data: businessReviews, error: businessError } = await businessQuery

      if (businessError) {
        console.error('Error fetching business reviews:', businessError)
        return NextResponse.json(
          { error: 'Failed to fetch business reviews' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        reviews: businessReviews || [],
        review_type: 'business',
        pagination: {
          limit,
          offset,
          total: businessReviews?.length || 0
        }
      })
    }

  } catch (error) {
    console.error('Error in reviews GET API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function updateBusinessRating(businessId: number) {
  try {
    // Calculate new average rating
    const { data: ratingData } = await supabase
      .from('reviews')
      .select('rating')
      .eq('business_id', businessId)

    if (ratingData && ratingData.length > 0) {
      const totalRating = ratingData.reduce((sum, review) => sum + review.rating, 0)
      const averageRating = totalRating / ratingData.length
      const reviewCount = ratingData.length

      // Update business rating and review count
      await supabase
        .from('businesses')
        .update({
          rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
          review_count: reviewCount
        })
        .eq('id', businessId)
    }
  } catch (error) {
    console.error('Error updating business rating:', error)
    // Don't throw error as this is not critical for the review submission
  }
}

async function updateDriverRating(driverId: string) {
  try {
    // Calculate new average rating for driver
    const { data: ratingData } = await supabase
      .from('driver_ratings')
      .select('rating')
      .eq('driver_id', driverId)

    if (ratingData && ratingData.length > 0) {
      const totalRating = ratingData.reduce((sum, rating) => sum + rating.rating, 0)
      const averageRating = totalRating / ratingData.length
      const ratingCount = ratingData.length

      // Update driver profile with new average rating
      await supabase
        .from('driver_profiles')
        .update({
          average_rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
          rating_count: ratingCount
        })
        .eq('id', driverId)
    }
  } catch (error) {
    console.error('Error updating driver rating:', error)
    // Don't throw error as this is not critical for the rating submission
  }
}
