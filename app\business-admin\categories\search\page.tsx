"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Search, Star, StarOff, RefreshCw, Filter, HelpCircle, X } from "lucide-react"
import { toast } from "sonner"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

interface Category {
  id: number
  name: string
  slug: string
  description?: string
  business_type_id?: number
  business_type_name?: string
  category_purpose: string
  display_order: number
  is_subscribed: boolean
  is_primary: boolean
}

export default function SearchCategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterBusinessType, setFilterBusinessType] = useState("all")

  const [filterSubscription, setFilterSubscription] = useState("all")

  // Fetch categories
  const fetchCategories = async () => {
    setLoading(true)
    try {
      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';
      console.log('Auth token found:', token ? 'Yes' : 'No', token ? `Length: ${token.length}` : '')

      const response = await fetch('/api/business-admin/category-subscriptions', {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      console.log('Categories API response status:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('Categories data received:', data)
        setCategories(data)
      } else {
        const errorText = await response.text()
        console.error('Categories API error:', response.status, errorText)
        toast.error(`Failed to load categories: ${response.status}`)
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
      toast.error('Failed to load categories')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCategories()
  }, [])

  // Handle subscription toggle
  const handleSubscriptionToggle = async (categoryId: number, isCurrentlySubscribed: boolean) => {
    try {
      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      if (isCurrentlySubscribed) {
        // Unsubscribe
        const response = await fetch(`/api/business-admin/category-subscriptions?category_id=${categoryId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': token ? `Bearer ${token}` : '',
          }
        })

        if (response.ok) {
          toast.success('Unsubscribed from category')
          fetchCategories()
        } else {
          const error = await response.json()
          toast.error(error.error || 'Failed to unsubscribe')
        }
      } else {
        // Subscribe
        const response = await fetch('/api/business-admin/category-subscriptions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : '',
          },
          body: JSON.stringify({ category_id: categoryId }),
        })

        if (response.ok) {
          toast.success('Subscribed to category')
          fetchCategories()
        } else {
          const error = await response.json()
          toast.error(error.error || 'Failed to subscribe')
        }
      }
    } catch (error) {
      console.error('Error toggling subscription:', error)
      toast.error('Failed to update subscription')
    }
  }

  // Handle primary category toggle
  const handlePrimaryToggle = async (categoryId: number) => {
    try {
      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      const response = await fetch('/api/business-admin/category-subscriptions/primary', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify({ category_id: categoryId }),
      })

      if (response.ok) {
        toast.success('Primary category updated')
        fetchCategories()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to update primary category')
      }
    } catch (error) {
      console.error('Error setting primary category:', error)
      toast.error('Failed to update primary category')
    }
  }

  // Filter categories
  const filteredCategories = categories.filter(category => {
    const matchesSearch = category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         category.slug.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesBusinessType = filterBusinessType === "all" ||
                               category.business_type_name?.toLowerCase() === filterBusinessType.toLowerCase()
    const matchesSubscription = filterSubscription === "all" ||
                               (filterSubscription === "subscribed" && category.is_subscribed) ||
                               (filterSubscription === "unsubscribed" && !category.is_subscribed)

    return matchesSearch && matchesBusinessType && matchesSubscription
  })

  // Get unique business types for filters
  const businessTypes = [...new Set(categories.map(cat => cat.business_type_name).filter(Boolean))]

  // Calculate summary stats
  const subscribedCount = categories.filter(cat => cat.is_subscribed).length
  const primaryCategory = categories.find(cat => cat.is_primary)

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Search Categories</h1>
          <p className="text-gray-600 mt-1">
            Subscribe to relevant categories to help customers find your business in search results
          </p>
        </div>

        <div className="flex gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="icon">
                <HelpCircle className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Search Categories Help</DialogTitle>
                <DialogDescription>
                  Understanding how category subscriptions work
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">What are Search Categories?</h4>
                  <p className="text-sm text-gray-600">
                    Search categories are Loop platform categories that customers can browse to find businesses.
                    When you subscribe to a category, your business will appear when customers search or browse that category.
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Statistics Explained</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li><strong>Subscribed Categories:</strong> Number of categories you're currently subscribed to</li>
                    <li><strong>Primary Category:</strong> Your main business category that affects search ranking</li>
                    <li><strong>Available Categories:</strong> Categories available for your business type</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">How Categories Work</h4>
                  <p className="text-sm text-gray-600">
                    Categories are horizontal filters that customers use to find businesses. You can only subscribe to
                    categories that are relevant to your business type. For example, shops see categories like Groceries,
                    Frozen Foods, and Personal Care, while restaurants see Pizza, Italian Cuisine, and Burgers.
                  </p>
                  <p className="text-xs text-gray-500 mt-2">
                    Note: These are different from your business page categories (the red buttons).
                    Search categories help customers find your business, while page categories organize your products.
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Best Practices</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Subscribe to categories that accurately represent your business or products</li>
                    <li>• Set one primary category that best describes your main business type</li>
                    <li>• Choose categories customers would logically search for when looking for your business</li>
                    <li>• Don't over-subscribe - focus on the most relevant categories</li>
                    <li>• Remember: These control search visibility, not your business page layout</li>
                  </ul>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Button variant="outline" onClick={fetchCategories}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Your Subscriptions</p>
                <p className="text-2xl font-bold text-emerald-600">{subscribedCount}</p>
                <p className="text-xs text-gray-500">categories subscribed</p>
              </div>
              <div className="h-8 w-8 bg-emerald-100 rounded-full flex items-center justify-center">
                <Star className="h-4 w-4 text-emerald-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Primary Category</p>
                <p className="text-lg font-semibold text-gray-900">
                  {primaryCategory ? primaryCategory.name : 'None set'}
                </p>
                <p className="text-xs text-gray-500">main business type</p>
              </div>
              <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Star className="h-4 w-4 text-blue-600 fill-current" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Available Categories</p>
                <p className="text-2xl font-bold text-gray-900">{categories.length}</p>
                <p className="text-xs text-gray-500">for your business type</p>
              </div>
              <div className="h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center">
                <Filter className="h-4 w-4 text-gray-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Search & Filter Categories</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search categories..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={filterBusinessType} onValueChange={setFilterBusinessType}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="All Business Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Business Types</SelectItem>
                {businessTypes.map(type => (
                  <SelectItem key={type} value={type!.toLowerCase()}>{type}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filterSubscription} onValueChange={setFilterSubscription}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="subscribed">Subscribed</SelectItem>
                <SelectItem value="unsubscribed">Not Subscribed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Categories Grid */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">
            Categories ({filteredCategories.length})
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {loading ? (
            Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    <div className="h-8 bg-gray-200 rounded w-20"></div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : filteredCategories.length === 0 ? (
            <div className="col-span-full text-center py-8">
              <p className="text-gray-500">No categories found</p>
            </div>
          ) : (
            filteredCategories.map((category) => (
              <Card key={category.id} className={`transition-all duration-200 ${
                category.is_subscribed ? 'ring-2 ring-emerald-200 bg-emerald-50' : 'hover:shadow-md'
              }`}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        {category.name}
                        {category.is_primary && (
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        )}
                      </h3>
                      {category.description && (
                        <p className="text-sm text-gray-600 mt-1">{category.description}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-3">
                    {category.business_type_name && (
                      <Badge variant="outline" className="text-xs">
                        {category.business_type_name}
                      </Badge>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <Button
                      size="sm"
                      variant={category.is_subscribed ? "destructive" : "default"}
                      onClick={() => handleSubscriptionToggle(category.id, category.is_subscribed)}
                      className="flex-1 mr-2"
                    >
                      {category.is_subscribed ? 'Unsubscribe' : 'Subscribe'}
                    </Button>

                    {category.is_subscribed && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handlePrimaryToggle(category.id)}
                        className="p-2"
                        title={category.is_primary ? "Remove as primary" : "Set as primary"}
                      >
                        {category.is_primary ? (
                          <StarOff className="h-4 w-4" />
                        ) : (
                          <Star className="h-4 w-4" />
                        )}
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </div>
  )
}
