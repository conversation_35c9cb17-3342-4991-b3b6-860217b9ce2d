"use client"

import { useState, useEffect } from 'react'
import { BusinessReviewManager } from '@/components/reviews/BusinessReviewManager'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Star, MessageSquare } from 'lucide-react'

// This is a demo page - in a real app, you'd get the business ID from authentication
const DEMO_BUSINESS_ID = 4 // Jersey Grill
const DEMO_BUSINESS_NAME = "Jersey Grill"

export default function BusinessReviewsPage() {
  const [stats, setStats] = useState({
    totalReviews: 0,
    averageRating: 0,
    pendingReplies: 0
  })

  useEffect(() => {
    fetchReviewStats()
  }, [])

  const fetchReviewStats = async () => {
    try {
      const response = await fetch(`/api/reviews?business_id=${DEMO_BUSINESS_ID}&limit=100`)
      const data = await response.json()
      
      if (data.success && data.reviews) {
        const reviews = data.reviews
        const totalReviews = reviews.length
        const averageRating = totalReviews > 0 
          ? Math.round((reviews.reduce((sum: number, review: any) => sum + review.rating, 0) / totalReviews) * 10) / 10
          : 0
        const pendingReplies = reviews.filter((review: any) => !review.business_reply).length

        setStats({
          totalReviews,
          averageRating,
          pendingReplies
        })
      }
    } catch (error) {
      console.error('Error fetching review stats:', error)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Business Reviews</h1>
        <p className="text-gray-600">
          Manage customer reviews and replies for {DEMO_BUSINESS_NAME}
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Reviews</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalReviews}</div>
            <p className="text-xs text-muted-foreground">
              Customer feedback received
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-yellow-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold flex items-center gap-1">
              {stats.averageRating}
              <Star className="h-5 w-5 text-yellow-400 fill-yellow-400" />
            </div>
            <p className="text-xs text-muted-foreground">
              Out of 5 stars
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Replies</CardTitle>
            <Badge variant={stats.pendingReplies > 0 ? "destructive" : "secondary"}>
              {stats.pendingReplies}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingReplies}</div>
            <p className="text-xs text-muted-foreground">
              Reviews awaiting response
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Review Management */}
      <Card>
        <CardHeader>
          <CardTitle>Review Management</CardTitle>
          <p className="text-sm text-gray-600">
            View and respond to customer reviews. Replies will be sent to customers and displayed publicly.
          </p>
        </CardHeader>
        <CardContent>
          <BusinessReviewManager 
            businessId={DEMO_BUSINESS_ID}
            businessName={DEMO_BUSINESS_NAME}
          />
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card className="mt-8 bg-blue-50 border-blue-200">
        <CardContent className="p-6">
          <h3 className="font-semibold text-blue-900 mb-2">How Review Replies Work</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Click "Reply to Customer" on any review to respond</li>
            <li>• Your reply will be sent as a message to the customer</li>
            <li>• Replies also appear publicly on your business page</li>
            <li>• Customers get notified when you reply to their review</li>
            <li>• Professional, helpful replies can improve your business reputation</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
