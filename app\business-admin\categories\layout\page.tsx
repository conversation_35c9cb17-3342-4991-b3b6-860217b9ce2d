'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { HelpCircle, AlertTriangle, CheckCircle, Info } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/context/unified-auth-context"
import { useBusinessData } from "@/hooks/use-business-data"

interface Business {
  id: number
  name: string
  page_layout: 'standard' | 'aisle'
}

interface Category {
  id: number
  name: string
  level: number
  parent_category: string | null
}

interface Product {
  id: number
  name: string
  custom_category_id: number
}

export default function CategoriesLayoutPage() {
  const { user } = useAuth()
  const { toast } = useToast()
  const {
    business,
    selectedBusinessId,
    isAdminUser,
    isLoading: businessLoading
  } = useBusinessData()

  const [selectedLayout, setSelectedLayout] = useState<'standard' | 'aisle'>('standard')
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [isHelpDialogOpen, setIsHelpDialogOpen] = useState(false)
  const [showWarningDialog, setShowWarningDialog] = useState(false)
  const [pendingLayout, setPendingLayout] = useState<'standard' | 'aisle'>('standard')

  useEffect(() => {
    if (user && !businessLoading && business) {
      fetchBusinessData()
      setSelectedLayout(business.page_layout || 'standard')
    }
  }, [user, businessLoading, business, selectedBusinessId])

  const fetchBusinessData = async () => {
    try {
      setIsLoading(true)

      // For admin users, require a business ID
      if (isAdminUser && !selectedBusinessId) {
        console.log("Admin user but no business selected, skipping fetch")
        setIsLoading(false)
        return
      }

      const token = localStorage.getItem('loop_jersey_auth_token') || ''

      // Build URLs with business ID for admin users
      let categoriesUrl = '/api/business-admin/custom-categories'
      let productsUrl = '/api/business-admin/products'

      if (isAdminUser && selectedBusinessId) {
        categoriesUrl += `?businessId=${selectedBusinessId}`
        productsUrl += `?businessId=${selectedBusinessId}`
      }

      // Fetch categories
      const categoriesResponse = await fetch(categoriesUrl, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache'
        }
      })
      if (!categoriesResponse.ok) throw new Error('Failed to fetch categories')
      const categoriesData = await categoriesResponse.json()
      setCategories(categoriesData.categories || [])

      // Fetch products
      const productsResponse = await fetch(productsUrl, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache'
        }
      })
      if (!productsResponse.ok) throw new Error('Failed to fetch products')
      const productsData = await productsResponse.json()
      setProducts(productsData.products || [])

    } catch (error) {
      console.error('Error fetching business data:', error)
      toast({
        title: "Error",
        description: "Failed to load business data",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const validateAisleLayout = () => {
    const level1Categories = categories.filter(cat => cat.level === 1)
    const level0Categories = categories.filter(cat => cat.level === 0)
    const productsInLevel0 = products.filter(product => {
      const category = categories.find(cat => cat.id === product.custom_category_id)
      return category && category.level === 0
    })

    return {
      hasLevel1Categories: level1Categories.length > 0,
      hasLevel0Categories: level0Categories.length > 0,
      productsInLevel0Count: productsInLevel0.length,
      level1Count: level1Categories.length,
      level0Count: level0Categories.length
    }
  }

  const handleLayoutChange = (newLayout: 'standard' | 'aisle') => {
    if (newLayout === 'aisle') {
      const validation = validateAisleLayout()
      if (!validation.hasLevel1Categories || validation.productsInLevel0Count > 0) {
        setPendingLayout(newLayout)
        setShowWarningDialog(true)
        return
      }
    }

    setSelectedLayout(newLayout)
  }

  const handleSaveLayout = async () => {
    try {
      setIsSaving(true)

      const response = await fetch('/api/business-admin/business/layout', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          page_layout: selectedLayout
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update layout')
      }

      toast({
        title: "Success",
        description: `Layout updated to ${selectedLayout}`,
      })

      // Refresh business data
      await fetchBusinessData()

    } catch (error) {
      console.error('Error updating layout:', error)
      toast({
        title: "Error",
        description: "Failed to update layout",
        variant: "destructive"
      })
    } finally {
      setIsSaving(false)
    }
  }

  const proceedWithAisleLayout = () => {
    setSelectedLayout(pendingLayout)
    setShowWarningDialog(false)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading business data...</p>
        </div>
      </div>
    )
  }

  const validation = validateAisleLayout()
  const hasChanges = business && selectedLayout !== business.page_layout

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Page Layout</h1>
          <p className="text-gray-600 mt-1">
            Choose how your business page displays categories and products
          </p>
        </div>

        <Dialog open={isHelpDialogOpen} onOpenChange={setIsHelpDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" className="flex items-center gap-2">
              <HelpCircle className="h-4 w-4" />
              Help
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Page Layout Options</DialogTitle>
              <DialogDescription>
                Understanding the different layout options for your business page
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-emerald-600 mb-2">Standard Layout</h4>
                <p className="text-sm text-gray-600 mb-2">
                  Best for businesses with fewer than 100 items like cafes and restaurants.
                </p>
                <ul className="text-sm text-gray-600 space-y-1 ml-4">
                  <li>• All categories and products displayed on one page</li>
                  <li>• Simple navigation with category menu</li>
                  <li>• Quick browsing for smaller inventories</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-emerald-600 mb-2">Aisle Layout</h4>
                <p className="text-sm text-gray-600 mb-2">
                  Designed for larger inventories like supermarkets with hundreds or thousands of products.
                </p>
                <ul className="text-sm text-gray-600 space-y-1 ml-4">
                  <li>• Two-level category hierarchy (aisles → subcategories)</li>
                  <li>• Level 0 categories act as main aisles</li>
                  <li>• Level 1 categories organize products within each aisle</li>
                  <li>• Customers navigate: Business Page → Aisle → Subcategory → Products</li>
                </ul>
              </div>

              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <strong>Requirements for Aisle Layout:</strong>
                  <br />• You must have Level 1 categories created
                  <br />• All products must be assigned to Level 1 categories
                  <br />• Use the Product Import tool to reassign products if needed
                </AlertDescription>
              </Alert>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {business && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Current Layout:
              <Badge variant={business.page_layout === 'aisle' ? 'default' : 'secondary'}>
                {business.page_layout === 'aisle' ? 'Aisle' : 'Standard'}
              </Badge>
            </CardTitle>
            <CardDescription>
              {business.name} is currently using the {business.page_layout} layout
            </CardDescription>
          </CardHeader>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Choose Layout Type</CardTitle>
          <CardDescription>
            Select the layout that best fits your business type and inventory size
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <RadioGroup value={selectedLayout} onValueChange={handleLayoutChange}>
            <div className="space-y-4">
              <div className="flex items-start space-x-3 p-4 border rounded-lg">
                <RadioGroupItem value="standard" id="standard" className="mt-1" />
                <div className="flex-1">
                  <Label htmlFor="standard" className="text-base font-medium cursor-pointer">
                    Standard Layout
                  </Label>
                  <p className="text-sm text-gray-600 mt-1">
                    Perfect for cafes, restaurants, and businesses with fewer than 100 items.
                    All categories and products are displayed on a single page with a simple menu.
                  </p>
                  <div className="mt-2 flex items-center gap-2">
                    <Badge variant="outline">Recommended for: Restaurants, Cafes, Small Shops</Badge>
                  </div>
                </div>
              </div>

              <div className="flex items-start space-x-3 p-4 border rounded-lg">
                <RadioGroupItem value="aisle" id="aisle" className="mt-1" />
                <div className="flex-1">
                  <Label htmlFor="aisle" className="text-base font-medium cursor-pointer">
                    Aisle Layout
                  </Label>
                  <p className="text-sm text-gray-600 mt-1">
                    Designed for supermarkets and large retailers with hundreds or thousands of products.
                    Uses a two-level hierarchy with main aisles and subcategories.
                  </p>
                  <div className="mt-2 flex items-center gap-2">
                    <Badge variant="outline">Recommended for: Supermarkets, Large Retailers</Badge>
                  </div>

                  {selectedLayout === 'aisle' && (
                    <div className="mt-3 space-y-2">
                      <div className="flex items-center gap-2">
                        {validation.hasLevel1Categories ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <AlertTriangle className="h-4 w-4 text-red-600" />
                        )}
                        <span className="text-sm">
                          Level 1 categories: {validation.level1Count} found
                        </span>
                      </div>

                      {validation.productsInLevel0Count > 0 && (
                        <div className="flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4 text-amber-600" />
                          <span className="text-sm">
                            {validation.productsInLevel0Count} products assigned to Level 0 categories
                          </span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </RadioGroup>

          {hasChanges && (
            <div className="flex items-center justify-between pt-4 border-t">
              <p className="text-sm text-gray-600">
                You have unsaved changes to your layout configuration.
              </p>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setSelectedLayout(business?.page_layout || 'standard')}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSaveLayout}
                  disabled={isSaving}
                  className="bg-emerald-600 hover:bg-emerald-700"
                >
                  {isSaving ? 'Saving...' : 'Save Layout'}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Warning Dialog for Aisle Layout */}
      <Dialog open={showWarningDialog} onOpenChange={setShowWarningDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-600" />
              Aisle Layout Requirements
            </DialogTitle>
            <DialogDescription>
              Your business needs to meet certain requirements before switching to aisle layout.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {!validation.hasLevel1Categories && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>No Level 1 categories found.</strong>
                  <br />You need to create Level 1 categories before using the aisle layout.
                  Level 1 categories will contain your products and be grouped under Level 0 aisles.
                </AlertDescription>
              </Alert>
            )}

            {validation.productsInLevel0Count > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>{validation.productsInLevel0Count} products are assigned to Level 0 categories.</strong>
                  <br />These products will not be displayed in aisle layout. You'll need to move them
                  to Level 1 categories using the Product Import tool (coming soon).
                </AlertDescription>
              </Alert>
            )}

            <p className="text-sm text-gray-600">
              You can continue with the aisle layout, but customers won't see products that aren't
              properly categorized. We recommend setting up your category hierarchy first.
            </p>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowWarningDialog(false)}>
              Cancel
            </Button>
            <Button onClick={proceedWithAisleLayout} className="bg-emerald-600 hover:bg-emerald-700">
              Continue Anyway
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
