// Simple test script to verify the context API
// Run with: node test-context-api.js

const testUserId = 'test-user-123'

async function testContextAPI() {
  try {
    console.log('Testing context API...')
    
    // Test the context endpoint
    const response = await fetch(`http://localhost:3000/api/messages/context?user_id=${testUserId}`)
    const data = await response.json()
    
    console.log('Response:', JSON.stringify(data, null, 2))
    
    if (data.success) {
      console.log('✅ API working!')
      console.log('User role:', data.context.user_role)
      console.log('Has active orders:', data.context.has_active_orders)
      console.log('Has recent completed orders:', data.context.has_recent_completed_orders)
      console.log('Has recent business interactions:', data.context.has_recent_business_interactions)
    } else {
      console.log('❌ API error:', data.error)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

// Run the test
testContextAPI()
