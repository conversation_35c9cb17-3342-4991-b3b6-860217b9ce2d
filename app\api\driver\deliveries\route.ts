import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    // For testing purposes, we'll use a hardcoded user email
    // In production, this would come from proper JWT validation
    const userEmail = '<EMAIL>' // Hardcoded for testing

    // Get user ID from email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, auth_id')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Get driver profile
    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .select('id, is_verified, is_active')
      .eq('user_id', user.id)
      .single()

    if (driverError || !driverProfile) {
      return NextResponse.json(
        { error: "Driver profile not found" },
        { status: 404 }
      )
    }

    if (!driverProfile.is_verified || !driverProfile.is_active) {
      return NextResponse.json(
        { error: "Driver not verified or inactive" },
        { status: 403 }
      )
    }

    // Check if driver is on shift
    const { data: driverStatus } = await supabase
      .from('driver_status')
      .select('is_on_shift, is_on_delivery')
      .eq('driver_id', driverProfile.id)
      .single()

    if (!driverStatus?.is_on_shift) {
      return NextResponse.json({
        success: true,
        deliveries: [],
        message: "Driver is not on shift - no delivery requests available"
      })
    }

    if (driverStatus.is_on_delivery) {
      return NextResponse.json({
        success: true,
        deliveries: [],
        message: "Driver is currently on a delivery"
      })
    }

    // First, get the businesses this driver is approved for
    const { data: approvedBusinesses, error: approvalsError } = await supabase
      .from('driver_business_approvals')
      .select('business_id')
      .eq('driver_id', driverProfile.id)
      .eq('status', 'approved')

    if (approvalsError) {
      console.error('Error fetching business approvals:', approvalsError)
      return NextResponse.json(
        { error: "Failed to fetch business approvals" },
        { status: 500 }
      )
    }

    // If driver has no approved businesses, return empty array
    if (!approvedBusinesses || approvedBusinesses.length === 0) {
      return NextResponse.json({
        success: true,
        deliveries: [],
        count: 0,
        message: "No business approvals - apply to businesses first",
        driverStatus: {
          isOnline: driverStatus.is_online,
          isOnDelivery: driverStatus.is_on_delivery
        }
      })
    }

    const approvedBusinessIds = approvedBusinesses.map(approval => approval.business_id)

    // Get available delivery requests for this driver
    // Orders that are 'offered' status and not yet assigned to any driver
    // AND from businesses the driver is approved for
    const { data: availableOrders, error: ordersError } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        business_id,
        delivery_address,
        delivery_postcode,
        delivery_fee,
        order_total,
        ready_time,
        created_at,
        businesses!inner (
          id,
          name,
          address,
          postcode
        )
      `)
      .eq('status', 'offered')
      .eq('delivery_method', 'delivery')
      .is('driver_id', null)
      .in('business_id', approvedBusinessIds)
      .order('created_at', { ascending: true })
      .limit(10)

    if (ordersError) {
      console.error('Error fetching available orders:', ordersError)
      return NextResponse.json(
        { error: "Failed to fetch delivery requests" },
        { status: 500 }
      )
    }

    // Transform orders into delivery request format
    const deliveryRequests = availableOrders?.map(order => ({
      id: order.id,
      orderNumber: order.order_number,
      businessName: order.businesses.name,
      pickupAddress: `${order.businesses.address}, ${order.businesses.postcode}`,
      deliveryAddress: `${order.delivery_address}, ${order.delivery_postcode}`,
      estimatedEarnings: order.delivery_fee || 5.00,
      readyTime: order.ready_time,
      expiresAt: null, // Could add expiry logic later
      orderTotal: order.order_total,
      createdAt: order.created_at
    })) || []

    return NextResponse.json({
      success: true,
      deliveries: deliveryRequests,
      count: deliveryRequests.length,
      driverStatus: {
        isOnline: driverStatus.is_online,
        isOnDelivery: driverStatus.is_on_delivery
      }
    })

  } catch (error) {
    console.error('Error in driver deliveries GET:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
