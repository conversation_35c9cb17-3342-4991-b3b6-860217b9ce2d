import { NextRequest, NextResponse } from 'next/server'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { serviceName, serviceCategory, description, customerName, customerEmail, notes } = body

    if (!serviceName || !description || !customerName || !customerEmail) {
      return NextResponse.json(
        { error: 'Service name, description, customer name, and email are required' },
        { status: 400 }
      )
    }

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerComponentClient({ cookies: () => cookieStore })

    // Get user session if available
    const { data: { session } } = await supabase.auth.getSession()

    // Check if user has already voted for this service
    const { data: existingVote, error: voteCheckError } = await supabase
      .from('user_votes')
      .select('id')
      .eq('user_email', customerEmail)
      .eq('request_type', 'service')
      .eq('request_name', serviceName)
      .single()

    if (voteCheckError && voteCheckError.code !== 'PGRST116') {
      console.error('Error checking existing vote:', voteCheckError)
      return NextResponse.json(
        { error: 'Failed to check existing votes' },
        { status: 500 }
      )
    }

    if (existingVote) {
      return NextResponse.json(
        { error: 'You have already voted for this service' },
        { status: 400 }
      )
    }

    // Check if this service already exists
    const { data: existingService, error: serviceError } = await supabase
      .from('service_requests')
      .select('id, vote_count')
      .eq('service_name', serviceName)
      .eq('status', 'pending')
      .single()

    if (serviceError && serviceError.code !== 'PGRST116') {
      console.error('Error checking existing service:', serviceError)
      return NextResponse.json(
        { error: 'Failed to check existing services' },
        { status: 500 }
      )
    }

    if (existingService) {
      // Service exists, increment vote count
      const { data: updatedRequest, error: updateError } = await supabase
        .from('service_requests')
        .update({
          vote_count: existingService.vote_count + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingService.id)
        .select()
        .single()

      if (updateError) {
        console.error('Error updating service request:', updateError)
        return NextResponse.json(
          { error: 'Failed to update service request' },
          { status: 500 }
        )
      }

      // Record this user's vote
      await supabase
        .from('user_votes')
        .insert({
          user_id: session?.user?.id || null,
          user_email: customerEmail,
          request_type: 'service',
          request_name: serviceName
        })

      return NextResponse.json({
        message: 'Vote added successfully',
        request: updatedRequest
      })
    }

    // Create new service request
    const { data: newRequest, error: insertError } = await supabase
      .from('service_requests')
      .insert({
        service_name: serviceName,
        service_category: serviceCategory,
        description: description,
        customer_name: customerName,
        customer_email: customerEmail,
        user_id: session?.user?.id || null,
        notes: notes || null,
        vote_count: 1,
        status: 'pending'
      })
      .select()
      .single()

    if (insertError) {
      console.error('Error creating service request:', insertError)
      return NextResponse.json(
        { error: 'Failed to create service request' },
        { status: 500 }
      )
    }

    // Record this user's vote for the new request
    await supabase
      .from('user_votes')
      .insert({
        user_id: session?.user?.id || null,
        user_email: customerEmail,
        request_type: 'service',
        request_name: serviceName
      })

    // Create admin notification
    try {
      const { data: adminUsers } = await supabase
        .from('users')
        .select('id')
        .eq('role', 'admin')

      if (adminUsers && adminUsers.length > 0) {
        const notifications = adminUsers.map(admin => ({
          admin_user_id: admin.id,
          type: 'service_request',
          title: 'New Service Request',
          message: `${customerName} requested: ${serviceName}`,
          action_url: `/admin/service-requests/${newRequest.id}`,
          priority: 'medium',
          related_table: 'service_requests',
          related_record_id: newRequest.id,
          metadata: {
            service_name: serviceName,
            customer_name: customerName,
            customer_email: customerEmail,
            request_id: newRequest.id
          }
        }))

        await supabase
          .from('admin_notifications')
          .insert(notifications)
      }
    } catch (notificationError) {
      console.error('Error creating admin notification:', notificationError)
      // Don't fail the request if notification fails
    }

    return NextResponse.json({
      message: 'Service request created successfully',
      request: newRequest
    })

  } catch (error) {
    console.error('Error in service requests API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')
    const sortBy = searchParams.get('sortBy') || 'vote_count'
    const order = searchParams.get('order') || 'desc'

    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerComponentClient({ cookies: () => cookieStore })

    // Get service requests sorted by vote count
    const { data: requests, error, count } = await supabase
      .from('service_requests')
      .select('*', { count: 'exact' })
      .eq('status', 'pending')
      .order(sortBy, { ascending: order === 'asc' })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching service requests:', error)
      return NextResponse.json(
        { error: 'Failed to fetch service requests' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      requests: requests || [],
      total: count || 0,
      limit,
      offset
    })

  } catch (error) {
    console.error('Error in service requests GET API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
