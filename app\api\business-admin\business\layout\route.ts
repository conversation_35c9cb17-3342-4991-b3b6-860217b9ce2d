import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyBusinessAdminAccess } from '@/lib/simple-auth'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Create a Supabase client with service role key for admin operations
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

// PATCH - Update business page layout
export async function PATCH(request: NextRequest) {
  try {
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const { user, profile: userProfile } = accessCheck

    const body = await request.json()
    const { page_layout } = body

    if (!page_layout || !['standard', 'aisle'].includes(page_layout)) {
      return NextResponse.json(
        { error: 'Valid page_layout is required (standard or aisle)' },
        { status: 400 }
      )
    }

    // Get business ID from business_managers table
    let businessId: number | null = null

    // Check if user is a business manager
    const { data: managerData, error: managerError } = await adminClient
      .from("business_managers")
      .select("business_id")
      .eq("user_id", userProfile.id)
      .single()

    if (managerData && !managerError) {
      businessId = managerData.business_id
    } else if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
      // Admin users can access any business, default to first business for now
      const { data: firstBusiness } = await adminClient
        .from('businesses')
        .select('id')
        .limit(1)
        .single()

      if (firstBusiness) {
        businessId = firstBusiness.id
      }
    }

    if (!businessId) {
      return NextResponse.json(
        { error: 'No business associated with this account' },
        { status: 400 }
      )
    }

    const { data: updatedBusiness, error } = await adminClient
      .from('businesses')
      .update({
        page_layout,
        updated_at: new Date().toISOString()
      })
      .eq('id', businessId)
      .select()
      .single()

    if (error) {
      console.error('Error updating business layout:', error)
      return NextResponse.json(
        { error: 'Failed to update business layout' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      business: updatedBusiness,
      message: `Business layout updated to ${page_layout}`
    })

  } catch (error) {
    console.error('Error in layout update:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
