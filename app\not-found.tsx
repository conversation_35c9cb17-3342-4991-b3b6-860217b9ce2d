'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Home, ArrowLeft, Search, MapPin } from 'lucide-react'
import WheelLogoIcon from '@/components/wheel-logo-icon'
import { useEffect } from 'react'

export default function NotFound() {
  // Hide the header using CSS when this component mounts
  useEffect(() => {
    document.documentElement.classList.add('hide-header')

    // Clean up when component unmounts
    return () => {
      document.documentElement.classList.remove('hide-header')
    }
  }, [])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="max-w-lg w-full text-center">
        {/* Loop Logo Section */}
        <div className="mb-8">
          <div className="inline-flex items-center justify-center mb-6">
            <div className="flex items-center bg-emerald-600 rounded-lg px-6 py-3 border border-emerald-500 shadow-lg">
              <div className="wheel-logo mr-3">
                <WheelLogoIcon
                  size={32}
                  color="white"
                  className="text-white w-8 h-8"
                />
              </div>
              <span className="text-2xl font-bold text-white">Loop</span>
            </div>
          </div>

          <div className="text-8xl font-bold text-emerald-600 mb-4">404</div>
          <h1 className="text-3xl font-bold text-gray-800 mb-3">Oops! Page Not Found</h1>
          <p className="text-gray-600 mb-8 leading-relaxed">
            Looks like this page took a wrong turn! Don&apos;t worry, we&apos;ll help you get back on track to find what you need in Jersey.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center mb-12">
          <Button
            variant="default"
            className="bg-emerald-600 hover:bg-emerald-700 text-white flex items-center gap-2 shadow-md"
            asChild
          >
            <Link href="/">
              <Home className="h-4 w-4" />
              Back to Home
            </Link>
          </Button>

          <Button
            variant="outline"
            className="border-emerald-600 text-emerald-600 hover:bg-emerald-50 flex items-center gap-2"
            onClick={() => window.history.back()}
          >
            <ArrowLeft className="h-4 w-4" />
            Go Back
          </Button>

          <Button
            variant="outline"
            className="border-emerald-600 text-emerald-600 hover:bg-emerald-50 flex items-center gap-2"
            asChild
          >
            <Link href="/search">
              <Search className="h-4 w-4" />
              Search
            </Link>
          </Button>
        </div>

        {/* Popular Services Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex items-center justify-center gap-2 mb-6">
            <MapPin className="h-5 w-5 text-emerald-600" />
            <h2 className="text-xl font-semibold text-gray-800">Popular Services in Jersey</h2>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <Link
              href="/search?type=restaurant"
              className="group p-4 bg-gradient-to-br from-red-50 to-red-100 rounded-lg hover:shadow-md transition-all duration-200 border border-red-200"
            >
              <div className="text-2xl mb-2">🍔</div>
              <div className="font-medium text-red-700 group-hover:text-red-800">Restaurants</div>
              <div className="text-xs text-red-600 mt-1">Food delivery</div>
            </Link>
            <Link
              href="/search?type=shop"
              className="group p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg hover:shadow-md transition-all duration-200 border border-blue-200"
            >
              <div className="text-2xl mb-2">🛍️</div>
              <div className="font-medium text-blue-700 group-hover:text-blue-800">Shops</div>
              <div className="text-xs text-blue-600 mt-1">Shopping delivery</div>
            </Link>
            <Link
              href="/search?type=pharmacy"
              className="group p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg hover:shadow-md transition-all duration-200 border border-green-200"
            >
              <div className="text-2xl mb-2">💊</div>
              <div className="font-medium text-green-700 group-hover:text-green-800">Pharmacies</div>
              <div className="text-xs text-green-600 mt-1">Medicine delivery</div>
            </Link>
            <Link
              href="/search?type=cafe"
              className="group p-4 bg-gradient-to-br from-amber-50 to-amber-100 rounded-lg hover:shadow-md transition-all duration-200 border border-amber-200"
            >
              <div className="text-2xl mb-2">☕</div>
              <div className="font-medium text-amber-700 group-hover:text-amber-800">Cafes</div>
              <div className="text-xs text-amber-600 mt-1">Coffee & treats</div>
            </Link>
          </div>

          {/* Jersey tagline */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-500 italic">
              "From burgers to errands — Jersey&apos;s fastest way to get things done"
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
