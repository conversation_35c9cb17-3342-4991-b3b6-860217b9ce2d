import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyBusinessAdminAccess } from '@/lib/simple-auth'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Create a Supabase client with service role key for admin operations
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

// GET - Fetch business data
export async function GET(request: NextRequest) {
  try {
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const { user, profile: userProfile } = accessCheck

    // Get business ID from business_managers table
    let businessId: number | null = null

    // Check if user is a business manager
    const { data: managerData, error: managerError } = await adminClient
      .from("business_managers")
      .select("business_id")
      .eq("user_id", userProfile.id)
      .single()

    if (managerData && !managerError) {
      businessId = managerData.business_id
    } else if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
      // Admin users can access any business, default to first business for now
      const { data: firstBusiness } = await adminClient
        .from('businesses')
        .select('id')
        .limit(1)
        .single()

      if (firstBusiness) {
        businessId = firstBusiness.id
      }
    }

    if (!businessId) {
      return NextResponse.json(
        { error: 'No business associated with this account' },
        { status: 400 }
      )
    }

    const { data: business, error } = await adminClient
      .from('businesses')
      .select('id, name, slug, page_layout, description, logo_url, banner_url, address, location, delivery_radius, preparation_time_minutes, rating, review_count, is_featured, created_at, updated_at')
      .eq('id', businessId)
      .single()

    if (error) {
      console.error('Error fetching business:', error)
      return NextResponse.json(
        { error: 'Failed to fetch business data' },
        { status: 500 }
      )
    }

    if (!business) {
      return NextResponse.json(
        { error: 'Business not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      business
    })

  } catch (error) {
    console.error('Error in business fetch:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
