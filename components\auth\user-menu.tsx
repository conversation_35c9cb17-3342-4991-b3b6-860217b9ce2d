"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import {
  User, LogOut, Settings, UserCircle, UserPlus, LogIn,
  Building2, ShieldCheck, Store, Truck, ShoppingBag, DollarSign, BarChart3, Smartphone, Heart
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useAuth } from "@/context/unified-auth-context"
import { supabase } from "@/lib/supabase"
import { getAuthHeaders } from '@/utils/auth-utils'
import PWAInstallButton from "@/components/pwa-install-button"

export default function UserMenu() {
  const { user, userProfile, userRole: authUserRole, signOut, isLoading: authLoading } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [userName, setUserName] = useState<string>("")
  const [initials, setInitials] = useState<string>("")
  const [mounted, setMounted] = useState(false)
  const [userRole, setUserRole] = useState<string | null>(null)
  const [isVerifiedDriver, setIsVerifiedDriver] = useState<boolean>(false)

  // Set mounted state after component mounts
  useEffect(() => {
    setMounted(true);
  }, [])

  // Always call this useEffect, but conditionally execute its logic
  useEffect(() => {
    if (!mounted) return; // Skip execution if not mounted yet

    if (user?.email) {
      // Default to using email as username
      const defaultName = user.email.split('@')[0] || "User"
      setUserName(defaultName)
      setInitials(getInitials(defaultName))

      // Set user role from auth context if available
      if (authUserRole) {
        // User role set from auth context
        setUserRole(authUserRole)
      }

      // Try to get the user's name and role from the API endpoint, but don't block on it
      const fetchUserProfile = async () => {
        try {
          console.log("UserMenu: Fetching user profile from API")

          // Get auth headers using our utility function
          const headers = await getAuthHeaders({
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          });

          // Use the API endpoint instead of direct database access
          const response = await fetch('/api/user/profile', {
            // Include credentials to ensure cookies are sent
            credentials: 'include',
            // Add headers for authentication and cache control
            headers
          })

          if (response.ok) {
            const result = await response.json()

            // Only update if we successfully got data
            if (result.data) {
              console.log("UserMenu: Successfully fetched user profile:", result.data)

              if (result.data.name) {
                setUserName(result.data.name)
                setInitials(getInitials(result.data.name))
              }

              if (result.data.role) {
                setUserRole(result.data.role)
              }

              // Check if user is a verified driver
              if (result.data.is_verified_driver) {
                setIsVerifiedDriver(result.data.is_verified_driver)
              }
            } else {
              console.log("UserMenu: API response ok but no data:", result)
            }
          } else {
            console.log("UserMenu: API response not ok:", response.status)
          }
        } catch (error) {
          // Silently fail - we already have fallback values set
          console.log("UserMenu: Could not fetch user profile data, using defaults", error)
        }
      }

      // Add a small delay to avoid race conditions with auth context
      const timeoutId = setTimeout(() => {
        fetchUserProfile()
      }, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [user, userProfile, authUserRole, mounted])

  const getInitials = (name: string): string => {
    return name
      .split(" ")
      .map(part => part.charAt(0))
      .join("")
      .toUpperCase()
      .substring(0, 2)
  }

  const handleLogout = async () => {
    setIsLoading(true)
    try {
      await signOut()
      router.push("/")
      router.refresh()
    } catch (error) {
      console.error("Error signing out:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // Render loading state if not mounted yet
  if (!mounted) {
    return (
      <div className="w-10 h-10 rounded-lg border border-gray-300 bg-emerald-600 animate-pulse"></div>
    );
  }

  // If no user, show account icon that links to login
  if (!user) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="lg" className="relative h-10 w-10 rounded-lg text-white hover:bg-emerald-700 border border-gray-300 bg-emerald-600 p-0">
            <User className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="end" forceMount>
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">Account</p>
              <p className="text-xs leading-none text-muted-foreground">
                Sign in to access your account
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <Link href="/login" className="cursor-pointer">
              <LogIn className="mr-2 h-4 w-4" />
              <span>Sign In</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link href="/register" className="cursor-pointer">
              <UserPlus className="mr-2 h-4 w-4" />
              <span>Register</span>
            </Link>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="lg" className="relative h-10 w-10 rounded-lg text-white hover:bg-emerald-700 border border-gray-300 p-0 overflow-hidden">
          <Avatar className="h-10 w-10 rounded-none">
            <AvatarImage src="" alt={userName} />
            <AvatarFallback className="bg-emerald-600 text-white rounded-none">
              {initials}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{userName}</p>
            <p className="text-xs leading-none text-muted-foreground">
              {user.email}
            </p>
          </div>
        </DropdownMenuLabel>
        {/* Customer Section */}
        <DropdownMenuSeparator />
        <DropdownMenuLabel className="font-normal text-xs text-muted-foreground">
          Customer
        </DropdownMenuLabel>
        <DropdownMenuItem asChild>
          <Link href="/account/orders" className="cursor-pointer">
            <ShoppingBag className="mr-2 h-4 w-4" />
            <span>My Orders</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/account/profile" className="cursor-pointer">
            <UserCircle className="mr-2 h-4 w-4" />
            <span>Profile</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/account/settings" className="cursor-pointer">
            <Settings className="mr-2 h-4 w-4" />
            <span>Settings</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/requests" className="cursor-pointer">
            <Heart className="mr-2 h-4 w-4" />
            <span>Requests</span>
          </Link>
        </DropdownMenuItem>
        <PWAInstallButton />

        {/* Role-based navigation */}
        {userRole && (
          <>
            {/* Business Section */}
            {(userRole === 'business_manager' || userRole === 'business_staff' || userRole === 'admin' || userRole === 'super_admin') && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuLabel className="font-normal text-xs text-muted-foreground">
                  Business
                </DropdownMenuLabel>
                <DropdownMenuItem asChild>
                  <Link href="/business-admin" className="cursor-pointer">
                    <Store className="mr-2 h-4 w-4" />
                    <span>Business Admin</span>
                  </Link>
                </DropdownMenuItem>
              </>
            )}

            {/* Driver Section */}
            {(userRole === 'driver' || isVerifiedDriver || userRole === 'admin' || userRole === 'super_admin') && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuLabel className="font-normal text-xs text-muted-foreground">
                  Driver
                </DropdownMenuLabel>
                <DropdownMenuItem asChild>
                  <Link href="/driver/dashboard" className="cursor-pointer">
                    <Truck className="mr-2 h-4 w-4" />
                    <span>Driver Dashboard</span>
                  </Link>
                </DropdownMenuItem>
                {(userRole === 'driver' || isVerifiedDriver) && (
                  <>
                    <DropdownMenuItem asChild>
                      <Link href="/driver/earnings" className="cursor-pointer">
                        <DollarSign className="mr-2 h-4 w-4" />
                        <span>Earnings</span>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/driver/analytics" className="cursor-pointer">
                        <BarChart3 className="mr-2 h-4 w-4" />
                        <span>Analytics</span>
                      </Link>
                    </DropdownMenuItem>
                  </>
                )}
                <DropdownMenuItem asChild>
                  <Link href="/driver-mobile/dashboard" className="cursor-pointer">
                    <Smartphone className="mr-2 h-4 w-4" />
                    <span>Driver Mobile</span>
                  </Link>
                </DropdownMenuItem>
              </>
            )}

            {/* Loop Admins Section */}
            {(userRole === 'admin' || userRole === 'super_admin') && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuLabel className="font-normal text-xs text-muted-foreground">
                  Loop Admins
                </DropdownMenuLabel>
                <DropdownMenuItem asChild>
                  <Link href="/admin" className="cursor-pointer">
                    <Building2 className="mr-2 h-4 w-4" />
                    <span>Admin Dashboard</span>
                  </Link>
                </DropdownMenuItem>
                {userRole === 'super_admin' && (
                  <DropdownMenuItem asChild>
                    <Link href="/super-admin" className="cursor-pointer">
                      <ShieldCheck className="mr-2 h-4 w-4" />
                      <span>Super Admin</span>
                    </Link>
                  </DropdownMenuItem>
                )}
              </>
            )}
          </>
        )}

        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="cursor-pointer"
          onClick={handleLogout}
          disabled={isLoading}
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>{isLoading ? "Signing out..." : "Sign out"}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
