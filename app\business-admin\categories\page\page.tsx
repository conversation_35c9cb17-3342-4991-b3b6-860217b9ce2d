'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Edit, Trash2, HelpCircle, AlertTriangle, CheckCircle, ArrowUp, ArrowDown } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/context/unified-auth-context"
import { useBusinessData } from "@/hooks/use-business-data"


interface Category {
  id: number
  name: string
  slug: string
  description: string | null
  level: number
  parent_category: string | null
  display_order: number
  is_active: boolean
  created_at: string
  updated_at: string
}

interface Business {
  id: number
  name: string
  page_layout: 'standard' | 'aisle'
}



export default function CustomCategoriesPage() {
  const { toast } = useToast()
  const { user, isAdmin, isSuperAdmin } = useAuth()
  const {
    business,
    selectedBusinessId,
    isAdminUser,
    isLoading: businessLoading,
    error: businessError
  } = useBusinessData()

  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [isHelpDialogOpen, setIsHelpDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null)

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    level: 0,
    parent_category: ''
  })

  // Business ID management is now handled by useBusinessData hook

  useEffect(() => {
    console.log('Main effect:', { user: !!user, selectedBusinessId, isAdminUser })
    if (user && !businessLoading) {
      fetchData()
    }
  }, [user, selectedBusinessId, isAdminUser, businessLoading])



  const fetchData = async () => {
    try {
      setIsLoading(true)

      // For admin users, require a business ID
      if (isAdminUser && !selectedBusinessId) {
        console.log("Admin user but no business selected, skipping fetch")
        setIsLoading(false)
        return
      }

      const token = localStorage.getItem('loop_jersey_auth_token') || ''

      // Build categories URL with business ID for admin users
      let categoriesUrl = '/api/business-admin/custom-categories'

      if (isAdminUser && selectedBusinessId) {
        categoriesUrl += `?businessId=${selectedBusinessId}`
        console.log('Using business ID:', selectedBusinessId)
      }

      console.log('Fetching categories from URL:', categoriesUrl)

      // Fetch categories (business data comes from useBusinessData hook)
      const categoriesResponse = await fetch(categoriesUrl, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache'
        }
      })
      if (!categoriesResponse.ok) throw new Error('Failed to fetch categories')
      const categoriesData = await categoriesResponse.json()
      setCategories(categoriesData.categories || [])

    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: "Error",
        description: "Failed to load categories",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateCategory = async () => {
    try {
      if (!formData.name.trim()) {
        toast({
          title: "Error",
          description: "Category name is required",
          variant: "destructive"
        })
        return
      }

      if (formData.level === 1 && !formData.parent_category) {
        toast({
          title: "Error",
          description: "Level 1 categories must have a parent category",
          variant: "destructive"
        })
        return
      }

      const token = localStorage.getItem('loop_jersey_auth_token') || ''

      const response = await fetch('/api/business-admin/custom-categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify({
          businessId: business?.id,
          name: formData.name.trim(),
          description: formData.description.trim() || null,
          level: formData.level,
          parent_category: formData.level === 1 ? formData.parent_category : null
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create category')
      }

      toast({
        title: "Success",
        description: "Category created successfully",
      })

      // Reset form and close dialog
      setFormData({ name: '', description: '', level: 0, parent_category: '' })
      setIsCreateDialogOpen(false)

      // Refresh categories
      await fetchData()

    } catch (error: any) {
      console.error('Error creating category:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to create category",
        variant: "destructive"
      })
    }
  }

  const handleEditCategory = async () => {
    try {
      if (!editingCategory || !formData.name.trim()) {
        toast({
          title: "Error",
          description: "Category name is required",
          variant: "destructive"
        })
        return
      }

      const token = localStorage.getItem('loop_jersey_auth_token') || ''

      const response = await fetch('/api/business-admin/custom-categories', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify({
          categoryId: editingCategory.id,
          businessId: business?.id,
          name: formData.name.trim(),
          description: formData.description.trim() || null
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update category')
      }

      toast({
        title: "Success",
        description: "Category updated successfully",
      })

      // Reset form and close dialog
      setFormData({ name: '', description: '', level: 0, parent_category: '' })
      setEditingCategory(null)
      setIsEditDialogOpen(false)

      // Refresh categories
      await fetchData()

    } catch (error: any) {
      console.error('Error updating category:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to update category",
        variant: "destructive"
      })
    }
  }

  const handleDeleteCategory = async () => {
    if (!categoryToDelete) return

    try {
      const token = localStorage.getItem('loop_jersey_auth_token') || ''

      const response = await fetch(`/api/business-admin/custom-categories?categoryId=${categoryToDelete.id}&businessId=${business?.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete category')
      }

      toast({
        title: "Success",
        description: "Category deleted successfully",
      })

      // Close dialog and reset state
      setIsDeleteDialogOpen(false)
      setCategoryToDelete(null)

      // Refresh categories
      await fetchData()

    } catch (error: any) {
      console.error('Error deleting category:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to delete category",
        variant: "destructive"
      })
    }
  }

  const openDeleteDialog = (category: Category) => {
    setCategoryToDelete(category)
    setIsDeleteDialogOpen(true)
  }



  const openEditDialog = (category: Category) => {
    setEditingCategory(category)
    setFormData({
      name: category.name,
      description: category.description || '',
      level: category.level,
      parent_category: category.parent_category || ''
    })
    setIsEditDialogOpen(true)
  }

  const resetForm = () => {
    setFormData({ name: '', description: '', level: 0, parent_category: '' })
    setEditingCategory(null)
  }

  const level0Categories = categories.filter(cat => cat.level === 0)
  const level1Categories = categories.filter(cat => cat.level === 1)

  const getParentCategoryName = (parentId: string | null) => {
    if (!parentId) return null
    const parent = categories.find(cat => cat.id.toString() === parentId)
    return parent?.name || null
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading categories...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Business Page Categories</h1>
          <p className="text-gray-600 mt-1">
            Manage custom categories for organizing products on your business page
          </p>
        </div>

        <div className="flex gap-2">
          <Dialog open={isHelpDialogOpen} onOpenChange={setIsHelpDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <HelpCircle className="h-4 w-4" />
                Help
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Custom Categories Guide</DialogTitle>
                <DialogDescription>
                  Understanding how custom categories work for your business page
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-emerald-600 mb-2">Category Levels</h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• <strong>Level 0:</strong> Main categories (aisles for supermarkets)</li>
                    <li>• <strong>Level 1:</strong> Subcategories that contain products</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-emerald-600 mb-2">Layout Types</h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• <strong>Standard Layout:</strong> All categories displayed on one page</li>
                    <li>• <strong>Aisle Layout:</strong> Level 0 → Level 1 → Products hierarchy</li>
                  </ul>
                </div>

                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Important:</strong> Products must be assigned to Level 1 categories to be visible in aisle layout.
                    Level 0 categories are for organization only.
                  </AlertDescription>
                </Alert>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-emerald-600 hover:bg-emerald-700">
                <Plus className="h-4 w-4 mr-2" />
                Add Category
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Category</DialogTitle>
                <DialogDescription>
                  Add a new custom category for your business page
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Category Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="e.g., Fresh Produce, Dairy & Eggs"
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Optional description for this category"
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="level">Category Level</Label>
                  <Select value={formData.level.toString()} onValueChange={(value) => setFormData({ ...formData, level: parseInt(value), parent_category: '' })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">Level 0 - Main Category</SelectItem>
                      <SelectItem value="1">Level 1 - Subcategory</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {formData.level === 1 && (
                  <div>
                    <Label htmlFor="parent">Parent Category *</Label>
                    <Select value={formData.parent_category} onValueChange={(value) => setFormData({ ...formData, parent_category: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select parent category" />
                      </SelectTrigger>
                      <SelectContent>
                        {level0Categories.map((category) => (
                          <SelectItem key={category.id} value={category.id.toString()}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => { setIsCreateDialogOpen(false); resetForm(); }}>
                  Cancel
                </Button>
                <Button onClick={handleCreateCategory} className="bg-emerald-600 hover:bg-emerald-700">
                  Create Category
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {business && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Current Layout:
              <Badge variant={business.page_layout === 'aisle' ? 'default' : 'secondary'}>
                {business.page_layout === 'aisle' ? 'Aisle' : 'Standard'}
              </Badge>
            </CardTitle>
            <CardDescription>
              {business.name} is using the {business.page_layout} layout
            </CardDescription>
          </CardHeader>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Level 0 Categories
              <Badge variant="outline">{level0Categories.length}</Badge>
            </CardTitle>
            <CardDescription>
              Main categories {business?.page_layout === 'aisle' ? '(aisles)' : ''}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {level0Categories.length === 0 ? (
              <p className="text-gray-500 text-sm">No Level 0 categories created yet</p>
            ) : (
              <div className="space-y-2">
                {level0Categories.map((category) => (
                  <div key={category.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{category.name}</h4>
                      {category.description && (
                        <p className="text-sm text-gray-600">{category.description}</p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditDialog(category)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openDeleteDialog(category)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Level 1 Categories
              <Badge variant="outline">{level1Categories.length}</Badge>
            </CardTitle>
            <CardDescription>
              Subcategories that contain products
            </CardDescription>
          </CardHeader>
          <CardContent>
            {level1Categories.length === 0 ? (
              <p className="text-gray-500 text-sm">No Level 1 categories created yet</p>
            ) : (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {level1Categories.map((category) => (
                  <div key={category.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{category.name}</h4>
                      <p className="text-sm text-gray-600">
                        Parent: {getParentCategoryName(category.parent_category) || 'None'}
                      </p>
                      {category.description && (
                        <p className="text-sm text-gray-500">{category.description}</p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditDialog(category)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openDeleteDialog(category)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Category</DialogTitle>
            <DialogDescription>
              Update the category information
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-name">Category Name *</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="e.g., Fresh Produce, Dairy & Eggs"
              />
            </div>

            <div>
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Optional description for this category"
                rows={3}
              />
            </div>

            {editingCategory && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Category level and parent cannot be changed after creation.
                  Create a new category if you need different hierarchy.
                </AlertDescription>
              </Alert>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => { setIsEditDialogOpen(false); resetForm(); }}>
              Cancel
            </Button>
            <Button onClick={handleEditCategory} className="bg-emerald-600 hover:bg-emerald-700">
              Update Category
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              Delete Category
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this category? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          {categoryToDelete && (
            <div className="py-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900">{categoryToDelete.name}</h4>
                <p className="text-sm text-gray-600">
                  Level {categoryToDelete.level} category
                  {categoryToDelete.level === 1 && categoryToDelete.parent_category && (
                    <span> • Parent: {getParentCategoryName(categoryToDelete.parent_category)}</span>
                  )}
                </p>
                {categoryToDelete.description && (
                  <p className="text-sm text-gray-500 mt-1">{categoryToDelete.description}</p>
                )}
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleDeleteCategory}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Delete Category
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
