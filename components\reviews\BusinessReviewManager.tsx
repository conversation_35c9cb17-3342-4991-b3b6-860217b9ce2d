"use client"

import { useState, useEffect } from 'react'
import { Star, MessageSquare, Calendar, User, Reply, Send } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'

interface Review {
  id: number
  rating: number
  comment: string | null
  created_at: string
  order_id: number
  user_id: string
  businesses?: {
    name: string
  }
  orders?: {
    order_number: string
  }
  has_reply?: boolean
  business_reply?: {
    content: string
    created_at: string
  }
}

interface BusinessReviewManagerProps {
  businessId: number
  businessName: string
  className?: string
}

export function BusinessReviewManager({ 
  businessId, 
  businessName,
  className 
}: BusinessReviewManagerProps) {
  const [reviews, setReviews] = useState<Review[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [replyingTo, setReplyingTo] = useState<number | null>(null)
  const [replyContent, setReplyContent] = useState('')
  const [submittingReply, setSubmittingReply] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    fetchReviews()
  }, [businessId])

  const fetchReviews = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/reviews?business_id=${businessId}&limit=50`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch reviews')
      }

      const data = await response.json()
      
      if (data.success) {
        setReviews(data.reviews || [])
      }
    } catch (err) {
      console.error('Error fetching reviews:', err)
      setError(err instanceof Error ? err.message : 'Failed to load reviews')
    } finally {
      setLoading(false)
    }
  }

  const handleReplySubmit = async (reviewId: number) => {
    if (!replyContent.trim()) {
      toast({
        title: "Reply Required",
        description: "Please enter a reply message.",
        variant: "destructive"
      })
      return
    }

    try {
      setSubmittingReply(true)
      
      // Find the review to get customer info
      const review = reviews.find(r => r.id === reviewId)
      if (!review) {
        throw new Error('Review not found')
      }

      // Send reply through Messages API
      const response = await fetch('/api/connections-hub/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          recipient_id: review.user_id,
          content: replyContent.trim(),
          subject: `Reply to your review of ${businessName}`,
          channel_type: 'review_reply',
          message_type: 'response',
          business_id: businessId.toString(),
          order_id: review.order_id,
          review_id: reviewId
        })
      })

      if (!response.ok) {
        throw new Error('Failed to send reply')
      }

      toast({
        title: "Reply Sent",
        description: "Your reply has been sent to the customer.",
        variant: "default"
      })

      // Reset reply state
      setReplyingTo(null)
      setReplyContent('')
      
      // Refresh reviews to show the reply
      fetchReviews()

    } catch (err) {
      console.error('Error sending reply:', err)
      toast({
        title: "Failed to Send Reply",
        description: err instanceof Error ? err.message : "Please try again.",
        variant: "destructive"
      })
    } finally {
      setSubmittingReply(false)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={cn(
              'h-4 w-4',
              star <= rating
                ? 'text-yellow-400 fill-yellow-400'
                : 'text-gray-300'
            )}
          />
        ))}
      </div>
    )
  }

  if (loading) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-48 mb-4"></div>
          {[1, 2, 3].map((i) => (
            <Card key={i} className="mb-4">
              <CardContent className="p-4">
                <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={cn("text-center py-8", className)}>
        <p className="text-red-600 mb-2">Failed to load reviews</p>
        <Button variant="outline" size="sm" onClick={fetchReviews}>
          Try Again
        </Button>
      </div>
    )
  }

  if (reviews.length === 0) {
    return (
      <div className={cn("text-center py-8", className)}>
        <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Reviews Yet</h3>
        <p className="text-gray-500">
          Your business hasn't received any reviews yet.
        </p>
      </div>
    )
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Customer Reviews ({reviews.length})</h2>
        <Badge variant="outline">
          {businessName}
        </Badge>
      </div>

      <div className="space-y-4">
        {reviews.map((review) => (
          <Card key={review.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  {renderStars(review.rating)}
                  <Badge variant="secondary" className="text-xs">
                    {review.rating}/5
                  </Badge>
                </div>
                <div className="flex items-center text-xs text-gray-500">
                  <Calendar className="h-3 w-3 mr-1" />
                  {formatDate(review.created_at)}
                </div>
              </div>

              {review.comment && (
                <div className="mb-3">
                  <p className="text-gray-700 leading-relaxed">
                    "{review.comment}"
                  </p>
                </div>
              )}

              <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                <div className="flex items-center">
                  <User className="h-3 w-3 mr-1" />
                  <span>Customer Review</span>
                </div>
              </div>

              {review.business_reply && (
                <>
                  <Separator className="my-3" />
                  <div className="bg-blue-50 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center text-xs font-medium text-blue-700">
                        <MessageSquare className="h-3 w-3 mr-1" />
                        Your Reply
                      </div>
                      <span className="text-xs text-gray-500">
                        {formatDate(review.business_reply.created_at)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-700">
                      {review.business_reply.content}
                    </p>
                  </div>
                </>
              )}

              {!review.business_reply && (
                <>
                  <Separator className="my-3" />
                  {replyingTo === review.id ? (
                    <div className="space-y-3">
                      <Textarea
                        placeholder="Write your reply to this customer..."
                        value={replyContent}
                        onChange={(e) => setReplyContent(e.target.value)}
                        className="min-h-[80px]"
                      />
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleReplySubmit(review.id)}
                          disabled={submittingReply}
                        >
                          <Send className="h-3 w-3 mr-1" />
                          {submittingReply ? 'Sending...' : 'Send Reply'}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setReplyingTo(null)
                            setReplyContent('')
                          }}
                          disabled={submittingReply}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setReplyingTo(review.id)}
                      className="flex items-center gap-1"
                    >
                      <Reply className="h-3 w-3" />
                      Reply to Customer
                    </Button>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
