import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    const { user_id } = await request.json()

    if (!user_id) {
      return NextResponse.json({ error: 'user_id is required' }, { status: 400 })
    }

    // Generate a valid UUID if the provided user_id is not a UUID
    let validUserId = user_id
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(user_id)) {
      // Create a deterministic UUID based on the input string
      const crypto = require('crypto')
      const hash = crypto.createHash('md5').update(user_id).digest('hex')
      validUserId = `${hash.slice(0,8)}-${hash.slice(8,12)}-${hash.slice(12,16)}-${hash.slice(16,20)}-${hash.slice(20,32)}`
    }

    // Create a test business if it doesn't exist
    const { data: existingBusiness } = await supabase
      .from('businesses')
      .select('id')
      .eq('name', 'Test Pizza Place')
      .single()

    let businessId = existingBusiness?.id

    if (!businessId) {
      const { data: newBusiness, error: businessError } = await supabase
        .from('businesses')
        .insert({
          name: 'Test Pizza Place',
          slug: 'test-pizza-place',
          address: '123 Test Street, St. Helier',
          location: 'St. Helier',
          coordinates: '(49.1919, -2.1071)', // Jersey coordinates
          business_type_id: 1, // Assuming restaurant type exists
          description: 'A test pizza restaurant for template testing',
          delivery_radius: 5.0,
          preparation_time_minutes: 20,
          rating: 4.5,
          review_count: 25,
          is_featured: false
        })
        .select('id')
        .single()

      if (businessError) {
        console.error('Error creating test business:', businessError)
        return NextResponse.json({
          error: 'Failed to create test business',
          details: businessError.message,
          code: businessError.code
        }, { status: 500 })
      }

      businessId = newBusiness.id
    }

    // Create a recent completed order (2 days ago)
    const twoDaysAgo = new Date()
    twoDaysAgo.setDate(twoDaysAgo.getDate() - 2)

    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert({
        user_id: validUserId,
        business_id: businessId,
        business_name: 'Test Pizza Place',
        business_type: 'restaurant',
        order_number: `TEST${Date.now()}`,
        status: 'delivered',
        customer_name: 'Test Customer',
        customer_phone: '+44 1534 123456',
        delivery_address: '456 Test Avenue, St. Helier',
        payment_method: 'card',
        payment_status: 'completed',
        total: 25.50,
        subtotal: 20.75,
        delivery_fee: 3.50,
        service_fee: 1.25,
        delivery_type: 'delivery',
        parish: 'St. Helier',
        postcode: 'JE2 4CD',
        created_at: twoDaysAgo.toISOString(),
        updated_at: new Date().toISOString(),
        delivered_at: new Date().toISOString()
      })
      .select('id, order_number')
      .single()

    if (orderError) {
      console.error('Error creating test order:', orderError)
      return NextResponse.json({
        error: 'Failed to create test order',
        details: orderError.message,
        code: orderError.code
      }, { status: 500 })
    }

    // Create some cart items for the order (using a dummy cart_id and product_id)
    const dummyCartId = '00000000-0000-0000-0000-000000000001'
    await supabase
      .from('cart_items')
      .insert([
        {
          cart_id: dummyCartId,
          product_id: 1, // Dummy product ID
          business_id: businessId,
          name: 'Margherita Pizza',
          quantity: 1,
          price: 18.00
        },
        {
          cart_id: dummyCartId,
          product_id: 2, // Dummy product ID
          business_id: businessId,
          name: 'Garlic Bread',
          quantity: 1,
          price: 7.50
        }
      ])

    // Create a recent business interaction (communication)
    const fiveDaysAgo = new Date()
    fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5)

    await supabase
      .from('communications')
      .insert({
        user_id: validUserId,
        business_id: businessId,
        message: 'Thank you for the great service!',
        created_at: fiveDaysAgo.toISOString()
      })

    return NextResponse.json({
      success: true,
      message: 'Test data created successfully',
      data: {
        business_id: businessId,
        order_id: order.id,
        order_number: order.order_number
      }
    })

  } catch (error) {
    console.error('Error creating test data:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { user_id } = await request.json()

    if (!user_id) {
      return NextResponse.json({ error: 'user_id is required' }, { status: 400 })
    }

    // Generate a valid UUID if the provided user_id is not a UUID
    let validUserId = user_id
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(user_id)) {
      // Create a deterministic UUID based on the input string
      const crypto = require('crypto')
      const hash = crypto.createHash('md5').update(user_id).digest('hex')
      validUserId = `${hash.slice(0,8)}-${hash.slice(8,12)}-${hash.slice(12,16)}-${hash.slice(16,20)}-${hash.slice(20,32)}`
    }

    // Delete test orders and related data
    const { data: orders } = await supabase
      .from('orders')
      .select('id')
      .eq('user_id', validUserId)
      .like('order_number', 'TEST%')

    if (orders && orders.length > 0) {
      const orderIds = orders.map(o => o.id)

      // Delete orders
      await supabase
        .from('orders')
        .delete()
        .in('id', orderIds)
    }

    // Delete test cart items (using dummy cart_id)
    const dummyCartId = '00000000-0000-0000-0000-000000000001'
    await supabase
      .from('cart_items')
      .delete()
      .eq('cart_id', dummyCartId)

    // Delete test communications
    await supabase
      .from('communications')
      .delete()
      .eq('user_id', validUserId)

    // Delete test business (optional - might want to keep for other tests)
    await supabase
      .from('businesses')
      .delete()
      .eq('slug', 'test-pizza-place')

    return NextResponse.json({
      success: true,
      message: 'Test data cleaned up successfully'
    })

  } catch (error) {
    console.error('Error cleaning up test data:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
