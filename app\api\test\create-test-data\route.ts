import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    const { user_id } = await request.json()

    if (!user_id) {
      return NextResponse.json({ error: 'user_id is required' }, { status: 400 })
    }

    // Create a test business if it doesn't exist
    const { data: existingBusiness } = await supabase
      .from('businesses')
      .select('id')
      .eq('name', 'Test Pizza Place')
      .single()

    let businessId = existingBusiness?.id

    if (!businessId) {
      const { data: newBusiness, error: businessError } = await supabase
        .from('businesses')
        .insert({
          name: 'Test Pizza Place',
          display_name: 'Test Pizza Place',
          email: '<EMAIL>',
          phone: '+44 1534 123456',
          address: '123 Test Street',
          parish: 'St. Helier',
          postcode: 'JE2 3AB',
          business_type: 'restaurant',
          cuisine_type: 'Italian',
          status: 'active'
        })
        .select('id')
        .single()

      if (businessError) {
        console.error('Error creating test business:', businessError)
        return NextResponse.json({ error: 'Failed to create test business' }, { status: 500 })
      }

      businessId = newBusiness.id
    }

    // Create a recent completed order (2 days ago)
    const twoDaysAgo = new Date()
    twoDaysAgo.setDate(twoDaysAgo.getDate() - 2)

    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert({
        customer_id: user_id,
        business_id: businessId,
        order_number: `TEST${Date.now()}`,
        status: 'delivered',
        total_amount: 25.50,
        delivery_fee: 3.50,
        service_fee: 1.25,
        created_at: twoDaysAgo.toISOString(),
        updated_at: new Date().toISOString()
      })
      .select('id, order_number')
      .single()

    if (orderError) {
      console.error('Error creating test order:', orderError)
      return NextResponse.json({ error: 'Failed to create test order' }, { status: 500 })
    }

    // Create some cart items for the order
    await supabase
      .from('cart_items')
      .insert([
        {
          order_id: order.id,
          item_name: 'Margherita Pizza',
          quantity: 1,
          price: 18.00,
          special_instructions: 'Extra cheese please'
        },
        {
          order_id: order.id,
          item_name: 'Garlic Bread',
          quantity: 1,
          price: 7.50
        }
      ])

    // Create a recent business interaction (communication)
    const fiveDaysAgo = new Date()
    fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5)

    await supabase
      .from('communications')
      .insert({
        user_id: user_id,
        business_id: businessId,
        message: 'Thank you for the great service!',
        created_at: fiveDaysAgo.toISOString()
      })

    return NextResponse.json({
      success: true,
      message: 'Test data created successfully',
      data: {
        business_id: businessId,
        order_id: order.id,
        order_number: order.order_number
      }
    })

  } catch (error) {
    console.error('Error creating test data:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { user_id } = await request.json()

    if (!user_id) {
      return NextResponse.json({ error: 'user_id is required' }, { status: 400 })
    }

    // Delete test orders and related data
    const { data: orders } = await supabase
      .from('orders')
      .select('id')
      .eq('customer_id', user_id)
      .like('order_number', 'TEST%')

    if (orders && orders.length > 0) {
      const orderIds = orders.map(o => o.id)
      
      // Delete cart items
      await supabase
        .from('cart_items')
        .delete()
        .in('order_id', orderIds)

      // Delete orders
      await supabase
        .from('orders')
        .delete()
        .in('id', orderIds)
    }

    // Delete test communications
    await supabase
      .from('communications')
      .delete()
      .eq('user_id', user_id)

    // Delete test business (optional - might want to keep for other tests)
    await supabase
      .from('businesses')
      .delete()
      .eq('name', 'Test Pizza Place')

    return NextResponse.json({
      success: true,
      message: 'Test data cleaned up successfully'
    })

  } catch (error) {
    console.error('Error cleaning up test data:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
