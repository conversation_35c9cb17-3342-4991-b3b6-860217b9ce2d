'use client'

import { useState } from 'react'
import { useAuth } from '@/context/unified-auth-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import {
  Bell,
  Mail,
  Phone,
  Shield,
  Eye,
  Trash2,
  Download,
  AlertTriangle
} from 'lucide-react'
import { SimplePushNotifications } from '@/components/simple-push-notifications'
import { MessagingSettings } from '@/components/messaging-settings'

export default function SettingsPage() {
  const { user, isLoading } = useAuth()

  // Settings state
  const [notifications, setNotifications] = useState({
    orderUpdates: true,
    promotions: false,
    newsletter: true,
    sms: false,
    push: true
  })

  const [privacy, setPrivacy] = useState({
    profileVisible: false,
    orderHistory: true,
    analytics: true
  })

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded animate-pulse" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Authentication Required</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">
            Please sign in to access your account settings.
          </p>
          <Button onClick={() => window.location.href = '/auth/signin'}>
            Sign In
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Account Settings</h1>
        <p className="text-gray-600">
          Manage your preferences and account security
        </p>
      </div>

      {/* Push Notifications */}
      <SimplePushNotifications />

      {/* Messaging Settings */}
      <MessagingSettings />

      {/* Other Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Other Notifications
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base font-medium">Newsletter</Label>
              <p className="text-sm text-gray-600">
                Weekly updates about new businesses and features
              </p>
            </div>
            <Switch
              checked={notifications.newsletter}
              onCheckedChange={(checked) =>
                setNotifications(prev => ({ ...prev, newsletter: checked }))
              }
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base font-medium">SMS Notifications</Label>
              <p className="text-sm text-gray-600">
                Receive text messages for urgent updates
              </p>
            </div>
            <Switch
              checked={notifications.sms}
              onCheckedChange={(checked) =>
                setNotifications(prev => ({ ...prev, sms: checked }))
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Privacy */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Privacy & Data
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base font-medium">Public Profile</Label>
              <p className="text-sm text-gray-600">
                Allow others to see your profile information
              </p>
            </div>
            <Switch
              checked={privacy.profileVisible}
              onCheckedChange={(checked) =>
                setPrivacy(prev => ({ ...prev, profileVisible: checked }))
              }
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base font-medium">Order History Visibility</Label>
              <p className="text-sm text-gray-600">
                Show order history in your profile
              </p>
            </div>
            <Switch
              checked={privacy.orderHistory}
              onCheckedChange={(checked) =>
                setPrivacy(prev => ({ ...prev, orderHistory: checked }))
              }
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base font-medium">Analytics & Insights</Label>
              <p className="text-sm text-gray-600">
                Help improve our service with usage analytics
              </p>
            </div>
            <Switch
              checked={privacy.analytics}
              onCheckedChange={(checked) =>
                setPrivacy(prev => ({ ...prev, analytics: checked }))
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Account Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Account Actions
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button variant="outline" className="w-full justify-start">
            <Download className="h-4 w-4 mr-2" />
            Download My Data
          </Button>

          <Button variant="outline" className="w-full justify-start">
            <Mail className="h-4 w-4 mr-2" />
            Change Email Address
          </Button>

          <Button variant="outline" className="w-full justify-start">
            <Phone className="h-4 w-4 mr-2" />
            Update Phone Number
          </Button>

          <Separator />

          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
              <div className="flex-1">
                <h3 className="font-medium text-red-900 mb-1">Danger Zone</h3>
                <p className="text-sm text-red-700 mb-3">
                  These actions cannot be undone. Please proceed with caution.
                </p>
                <Button variant="destructive" size="sm">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Account
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Save Changes */}
      <div className="flex justify-end">
        <Button className="bg-emerald-600 hover:bg-emerald-700">
          Save Changes
        </Button>
      </div>
    </div>
  )
}
