import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(request: Request) {
  try {
    const url = new URL(request.url)
    const email = url.searchParams.get('email') || '<EMAIL>'
    
    console.log("🔍 Debug: Looking up user with email:", email)
    
    // Try to find user by email
    const { data: userByEmail, error: emailError } = await adminClient
      .from("users")
      .select("*")
      .eq("email", email)
      .single()
    
    console.log("🔍 Debug: User by email result:", { userByEmail, emailError })
    
    // Also try to find all users with similar emails (case insensitive)
    const { data: allUsers, error: allError } = await adminClient
      .from("users")
      .select("id, email, name, role, business_id")
      .ilike("email", `%${email.toLowerCase()}%`)
    
    console.log("🔍 Debug: All similar users:", { allUsers, allError })
    
    return NextResponse.json({
      searchEmail: email,
      userByEmail,
      emailError,
      allUsers,
      allError
    })
    
  } catch (error) {
    console.error("Debug lookup error:", error)
    return NextResponse.json(
      { error: "Debug lookup failed", details: error },
      { status: 500 }
    )
  }
}
