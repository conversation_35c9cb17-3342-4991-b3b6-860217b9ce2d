import { NextResponse } from "next/server"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from "@supabase/supabase-js"
import { cookies } from "next/headers"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log("=== GET PRODUCT API CALLED ===")
    console.log("Product ID:", params.id)

    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the product ID from the URL params
    const productId = parseInt(params.id)

    // Get authentication token from request headers
    const authorization = request.headers.get('Authorization')
    if (!authorization) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    const token = authorization.replace('Bearer ', '')

    try {
      // Verify the token and get user's business
      const { data: { user }, error } = await adminClient.auth.getUser(token)

      if (error || !user) {
        console.error("Invalid token:", error)
        return NextResponse.json(
          { error: "Invalid authentication token" },
          { status: 401 }
        )
      }

      // Get user's business association
      const { data: userProfile, error: profileError } = await adminClient
        .from("users")
        .select("business_id, role")
        .eq("email", user.email)
        .single()

      if (profileError || !userProfile) {
        console.error("Error fetching user profile:", profileError)
        return NextResponse.json(
          { error: "User profile not found" },
          { status: 403 }
        )
      }

      console.log("Fetching product with proper authentication:", { productId, userRole: userProfile.role })

      // For non-admin users, ensure they can only access products from their business
      let businessId = null
      if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
        // Admin users can access any product
        console.log("Admin user accessing product")
      } else if (userProfile.business_id) {
        businessId = userProfile.business_id
        console.log("Business user accessing product from business:", businessId)
      } else {
        return NextResponse.json(
          { error: "No business associated with this account" },
          { status: 403 }
        )
      }

      // Fetch the product (simplified query without problematic relationships)
      let query = adminClient
        .from("products")
        .select(`
          id,
          name,
          description,
          price,
          image_url,
          custom_category_id,
          business_id,
          is_available,
          is_featured,
          is_popular,
          slug,
          unit,
          quantity,
          created_at,
          updated_at,
          business_custom_categories:custom_category_id(id, name)
        `)
        .eq("id", productId)

      // Apply business filter for non-admin users
      if (businessId) {
        query = query.eq("business_id", businessId)
      }

      const { data: product, error: productError } = await query.single()

      console.log("Product query result:", { product, productError })

      if (productError) {
        console.error("Error fetching product:", productError)
        return NextResponse.json(
          { error: "Product not found" },
          { status: 404 }
        )
      }

      return NextResponse.json({ product })
    } catch (authError) {
      console.error("Authentication error:", authError)
      return NextResponse.json(
        { error: "Authentication failed" },
        { status: 401 }
      )
    }
  } catch (error: any) {
    console.error(`Error in GET /api/business-admin/products/${params.id}:`, error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log("=== PATCH PRODUCT API CALLED ===")
    console.log("Product ID:", params.id)

    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the product ID from the URL params
    const productId = parseInt(params.id)

    // Get authentication token from request headers
    const authorization = request.headers.get('Authorization')
    if (!authorization) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    const token = authorization.replace('Bearer ', '')

    try {
      // Verify the token and get user's business
      const { data: { user }, error } = await adminClient.auth.getUser(token)

      if (error || !user) {
        console.error("Invalid token:", error)
        return NextResponse.json(
          { error: "Invalid authentication token" },
          { status: 401 }
        )
      }

      // Get user's business association
      const { data: userProfile, error: profileError } = await adminClient
        .from("users")
        .select("business_id, role")
        .eq("email", user.email)
        .single()

      if (profileError || !userProfile) {
        console.error("Error fetching user profile:", profileError)
        return NextResponse.json(
          { error: "User profile not found" },
          { status: 403 }
        )
      }

      console.log("Updating product with proper authentication:", { productId, userRole: userProfile.role })

      // For non-admin users, ensure they can only update products from their business
      let businessId = null
      if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
        // Admin users can update any product
        console.log("Admin user updating product")
      } else if (userProfile.business_id) {
        businessId = userProfile.business_id
        console.log("Business user updating product from business:", businessId)
      } else {
        return NextResponse.json(
          { error: "No business associated with this account" },
          { status: 403 }
        )
      }

      // Check if the product exists
      let existingProductQuery = adminClient
        .from("products")
        .select("id, business_id, name")
        .eq("id", productId)

      // Apply business filter for non-admin users
      if (businessId) {
        existingProductQuery = existingProductQuery.eq("business_id", businessId)
      }

      const { data: existingProduct, error: existingProductError } = await existingProductQuery.single()

      if (existingProductError || !existingProduct) {
        console.error("Product not found:", existingProductError)
        return NextResponse.json(
          { error: "Product not found" },
          { status: 404 }
        )
      }

      console.log("Found existing product:", existingProduct)

      // Parse the request body
      const requestData = await request.json()
      console.log("Request data:", requestData)

      const {
        name,
        description,
        price,
        image_url,
        custom_category_id,
        is_available,
        is_featured,
        is_popular,
        slug,
        unit,
        quantity
      } = requestData

      // Update the product
      const updateData: any = {
        updated_at: new Date().toISOString()
      }

      if (name !== undefined) {
        updateData.name = name
      }

      if (description !== undefined) {
        updateData.description = description
      }

      if (price !== undefined) {
        updateData.price = price
      }

      if (image_url !== undefined) {
        updateData.image_url = image_url
      }

      if (custom_category_id !== undefined) {
        updateData.custom_category_id = custom_category_id
      }

      if (is_available !== undefined) {
        updateData.is_available = is_available
      }

      if (is_featured !== undefined) {
        updateData.is_featured = is_featured
      }

      if (is_popular !== undefined) {
        updateData.is_popular = is_popular
      }

      if (slug !== undefined) {
        updateData.slug = slug
      }

      if (unit !== undefined) {
        updateData.unit = unit
      }

      if (quantity !== undefined) {
        updateData.quantity = quantity
      }

      console.log("Update data:", updateData)

      const { data: updatedProduct, error: updateError } = await adminClient
        .from("products")
        .update(updateData)
        .eq("id", productId)
        .select(`
          id,
          name,
          description,
          price,
          image_url,
          custom_category_id,
          is_available,
          is_featured,
          is_popular,
          slug,
          unit,
          quantity,
          created_at,
          updated_at,
          business_custom_categories:custom_category_id(id, name)
        `)
        .single()

      console.log("Update result:", { updatedProduct, updateError })

      if (updateError) {
        console.error("Error updating product:", updateError)
        return NextResponse.json(
          { error: "Failed to update product", details: updateError.message },
          { status: 500 }
        )
      }

      console.log("Product updated successfully:", updatedProduct)

      return NextResponse.json({
        success: true,
        product: updatedProduct,
        message: "Product updated successfully"
      })
    } catch (authError) {
      console.error("Authentication error:", authError)
      return NextResponse.json(
        { error: "Authentication failed" },
        { status: 401 }
      )
    }
  } catch (error: any) {
    console.error(`Error in PATCH /api/business-admin/products/${params.id}:`, error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // First, verify the user is authenticated and has business manager permissions
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError)
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the user's profile to check their role
    const { data: userProfile, error: userError } = await adminClient
      .from("users")
      .select("id, role, business_id")
      .eq("auth_id", session.user.id)
      .single()

    if (userError || !userProfile) {
      console.error("Error fetching user profile:", userError)
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 404 }
      )
    }

    // Check if the user is a business manager, admin, or super admin
    const isBusinessManager = userProfile.role === "business_manager"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    if (!isBusinessManager && !isAdmin && !isSuperAdmin) {
      console.error("User does not have business manager permissions")
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    // Get the business ID from the user profile
    const businessId = userProfile.business_id

    // Get the product ID from the URL params
    const productId = parseInt(params.id)

    // Check if the product exists and belongs to the user's business (for business managers)
    if (isBusinessManager) {
      const { data: existingProduct, error: existingProductError } = await adminClient
        .from("products")
        .select("id, business_id")
        .eq("id", productId)
        .single()

      if (existingProductError || !existingProduct) {
        console.error("Product not found:", existingProductError)
        return NextResponse.json(
          { error: "Product not found" },
          { status: 404 }
        )
      }

      if (existingProduct.business_id !== businessId) {
        console.error("Product does not belong to the user's business")
        return NextResponse.json(
          { error: "Product not found" },
          { status: 404 }
        )
      }
    }

    // First, delete variants
    await adminClient
      .from("product_variants")
      .delete()
      .eq("product_id", productId)

    // Then, delete the product
    const { error: deleteError } = await adminClient
      .from("products")
      .delete()
      .eq("id", productId)

    if (deleteError) {
      console.error("Error deleting product:", deleteError)
      return NextResponse.json(
        { error: "Failed to delete product" },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error: any) {
    console.error(`Error in DELETE /api/business-admin/products/${params.id}:`, error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
