-- Simplify driver shift system - Remove online/offline concepts
-- Focus on On-Shift/Off-Shift only with proper shift end reason tracking
-- Date: 2025-06-28

-- 1. Add shift_end_reason column to driver_shifts table
ALTER TABLE driver_shifts ADD COLUMN IF NOT EXISTS shift_end_reason VARCHAR(20) 
  CHECK (shift_end_reason IN ('manual', 'app_closed', 'disconnection_timeout'));

-- 2. Fix current data inconsistencies
-- Driver who is on_delivery but not on_shift should be put on_shift
UPDATE driver_status 
SET is_on_shift = TRUE, 
    last_status_change = NOW()
WHERE is_on_delivery = TRUE AND is_on_shift = FALSE;

-- 3. Remove is_online column from driver_status (simplify to just on_shift)
-- First, ensure any driver who was online but not on shift gets proper state
UPDATE driver_status 
SET is_on_shift = FALSE,
    current_location_lat = NULL,
    current_location_lng = NULL,
    location_updated_at = NULL,
    last_status_change = NOW()
WHERE is_online = TRUE AND is_on_shift = FALSE AND is_on_delivery = FALSE;

-- Now remove the is_online column
ALTER TABLE driver_status DROP COLUMN IF EXISTS is_online;

-- 4. Clean up any online/offline tracking tables that might exist
DROP TABLE IF EXISTS driver_online_history;

-- 5. Remove online/offline functions that might exist
DROP FUNCTION IF EXISTS update_driver_online_status(UUID, BOOLEAN);

-- 6. Update start_driver_shift function for simplified system
CREATE OR REPLACE FUNCTION start_driver_shift(p_driver_id UUID)
RETURNS INTEGER AS $$
DECLARE
  v_shift_id INTEGER;
  v_active_shift_count INTEGER;
BEGIN
  -- Check if driver already has an active shift
  SELECT COUNT(*) INTO v_active_shift_count
  FROM driver_shifts
  WHERE driver_id = p_driver_id AND shift_end IS NULL;
  
  IF v_active_shift_count > 0 THEN
    RAISE EXCEPTION 'Driver already has an active shift';
  END IF;
  
  -- Create new shift record with start time
  INSERT INTO driver_shifts (driver_id, shift_start)
  VALUES (p_driver_id, NOW())
  RETURNING id INTO v_shift_id;
  
  -- Update driver status to on shift
  UPDATE driver_status
  SET 
    is_on_shift = TRUE,
    last_status_change = NOW()
  WHERE driver_id = p_driver_id;
  
  RETURN v_shift_id;
END;
$$ LANGUAGE plpgsql;

-- 7. Update end_driver_shift function with shift_end_reason
CREATE OR REPLACE FUNCTION end_driver_shift(
  p_driver_id UUID,
  p_end_reason VARCHAR(20) DEFAULT 'manual'
)
RETURNS BOOLEAN AS $$
DECLARE
  v_shift_id INTEGER;
BEGIN
  -- Validate end reason
  IF p_end_reason NOT IN ('manual', 'app_closed', 'disconnection_timeout') THEN
    RAISE EXCEPTION 'Invalid shift end reason: %', p_end_reason;
  END IF;
  
  -- Get active shift
  SELECT id INTO v_shift_id
  FROM driver_shifts
  WHERE driver_id = p_driver_id AND shift_end IS NULL;
  
  IF v_shift_id IS NULL THEN
    RAISE EXCEPTION 'No active shift found for driver';
  END IF;
  
  -- Set shift end time and reason
  UPDATE driver_shifts
  SET 
    shift_end = NOW(),
    shift_end_reason = p_end_reason,
    updated_at = NOW()
  WHERE id = v_shift_id;
  
  -- Update driver status to off shift and clear location
  UPDATE driver_status
  SET 
    is_on_shift = FALSE,
    current_location_lat = NULL,
    current_location_lng = NULL,
    location_updated_at = NULL,
    last_status_change = NOW()
  WHERE driver_id = p_driver_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 8. Create function to handle app disconnection with grace period
CREATE OR REPLACE FUNCTION handle_driver_disconnection(
  p_driver_id UUID,
  p_grace_period_minutes INTEGER DEFAULT 5
)
RETURNS BOOLEAN AS $$
DECLARE
  v_is_on_shift BOOLEAN;
  v_last_activity TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Check if driver is currently on shift
  SELECT is_on_shift, last_status_change INTO v_is_on_shift, v_last_activity
  FROM driver_status
  WHERE driver_id = p_driver_id;
  
  -- Only process if driver is on shift
  IF NOT COALESCE(v_is_on_shift, FALSE) THEN
    RETURN FALSE;
  END IF;
  
  -- Check if grace period has expired
  IF v_last_activity + (p_grace_period_minutes || ' minutes')::INTERVAL < NOW() THEN
    -- End shift due to disconnection timeout
    PERFORM end_driver_shift(p_driver_id, 'disconnection_timeout');
    RETURN TRUE;
  END IF;
  
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- 9. Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_driver_shifts_end_reason ON driver_shifts(shift_end_reason) WHERE shift_end_reason IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_driver_status_on_shift ON driver_status(is_on_shift) WHERE is_on_shift = TRUE;

-- 10. Add comments for documentation
COMMENT ON COLUMN driver_shifts.shift_end_reason IS 'Reason for shift ending: manual (driver clicked off-shift), app_closed (app was closed), disconnection_timeout (5min grace period expired)';
COMMENT ON FUNCTION start_driver_shift(UUID) IS 'Start a new shift - simplified system with only on-shift/off-shift states';
COMMENT ON FUNCTION end_driver_shift(UUID, VARCHAR) IS 'End active shift with reason tracking for analytics';
COMMENT ON FUNCTION handle_driver_disconnection(UUID, INTEGER) IS 'Handle driver disconnection with configurable grace period before auto-ending shift';

-- 11. Update driver_activity_log to include shift events
-- Add shift start/end activity types for recent shifts
INSERT INTO driver_activity_log (driver_id, activity_type, timestamp, notes)
SELECT
  ds.driver_id,
  'shift_started',
  ds.shift_start,
  'Shift started - ID: ' || ds.id
FROM driver_shifts ds
WHERE ds.shift_start >= NOW() - INTERVAL '7 days'
  AND NOT EXISTS (
    SELECT 1 FROM driver_activity_log dal
    WHERE dal.driver_id = ds.driver_id
      AND dal.activity_type = 'shift_started'
      AND dal.timestamp = ds.shift_start
  );

INSERT INTO driver_activity_log (driver_id, activity_type, timestamp, notes)
SELECT
  ds.driver_id,
  CASE
    WHEN ds.shift_end_reason = 'manual' THEN 'shift_ended_manual'
    WHEN ds.shift_end_reason = 'app_closed' THEN 'shift_ended_app_closed'
    WHEN ds.shift_end_reason = 'disconnection_timeout' THEN 'shift_ended_timeout'
    ELSE 'shift_ended'
  END,
  ds.shift_end,
  'Shift ended - ID: ' || ds.id || ', Reason: ' || COALESCE(ds.shift_end_reason, 'unknown')
FROM driver_shifts ds
WHERE ds.shift_end IS NOT NULL
  AND ds.shift_end >= NOW() - INTERVAL '7 days'
  AND NOT EXISTS (
    SELECT 1 FROM driver_activity_log dal
    WHERE dal.driver_id = ds.driver_id
      AND dal.activity_type LIKE 'shift_ended%'
      AND dal.timestamp = ds.shift_end
  );
