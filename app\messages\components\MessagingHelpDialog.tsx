'use client'

import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  HelpCircle,
  MessageSquare,
  Users,
  ShoppingBag,
  Truck,
  Star,
  Shield,
  Clock,
  Settings
} from 'lucide-react'

interface MessagingHelpDialogProps {
  trigger?: React.ReactNode
}

export function MessagingHelpDialog({ trigger }: MessagingHelpDialogProps) {
  const [open, setOpen] = useState(false)

  const defaultTrigger = (
    <Button
      variant="ghost"
      size="sm"
      className="h-9 w-9 p-0 sm:w-auto sm:px-3"
    >
      <HelpCircle className="h-4 w-4" />
      <span className="hidden sm:inline sm:ml-2">Help</span>
    </Button>
  )

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-emerald-600" />
            Loop Messaging System
          </DialogTitle>
          <DialogDescription>
            Connect with businesses, drivers, and other customers for a better delivery experience
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* What is Loop Messaging */}
          <section>
            <h3 className="text-lg font-semibold mb-3">What is Loop Messaging?</h3>
            <p className="text-gray-600 mb-4">
              Loop Messaging connects you with everyone in the delivery ecosystem - businesses, drivers, and other customers. 
              Get real-time updates, ask questions, share feedback, and build relationships that make your delivery experience better.
            </p>
          </section>

          <Separator />

          {/* Who You Can Message */}
          <section>
            <h3 className="text-lg font-semibold mb-3">Who You Can Message</h3>
            <div className="grid gap-4 sm:grid-cols-3">
              <div className="flex flex-col items-center text-center p-4 bg-blue-50 rounded-lg">
                <ShoppingBag className="h-8 w-8 text-blue-600 mb-2" />
                <h4 className="font-medium">Businesses</h4>
                <p className="text-sm text-gray-600">Ask about menu items, special requests, or business hours</p>
              </div>
              <div className="flex flex-col items-center text-center p-4 bg-green-50 rounded-lg">
                <Truck className="h-8 w-8 text-green-600 mb-2" />
                <h4 className="font-medium">Drivers</h4>
                <p className="text-sm text-gray-600">Coordinate delivery details or provide special instructions</p>
              </div>
              <div className="flex flex-col items-center text-center p-4 bg-purple-50 rounded-lg">
                <Users className="h-8 w-8 text-purple-600 mb-2" />
                <h4 className="font-medium">Customers</h4>
                <p className="text-sm text-gray-600">Share recommendations or connect with your community</p>
              </div>
            </div>
          </section>

          <Separator />

          {/* Common Use Cases */}
          <section>
            <h3 className="text-lg font-semibold mb-3">Common Use Cases</h3>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <ShoppingBag className="h-5 w-5 text-emerald-600 mt-0.5" />
                <div>
                  <h4 className="font-medium">Order Communication</h4>
                  <p className="text-sm text-gray-600">Get updates about your order, ask about preparation time, or make special requests</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Truck className="h-5 w-5 text-emerald-600 mt-0.5" />
                <div>
                  <h4 className="font-medium">Delivery Coordination</h4>
                  <p className="text-sm text-gray-600">Share delivery instructions, coordinate timing, or track your driver's location</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Star className="h-5 w-5 text-emerald-600 mt-0.5" />
                <div>
                  <h4 className="font-medium">Reviews & Feedback</h4>
                  <p className="text-sm text-gray-600">Share your experience, leave reviews, or get recommendations from other customers</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Users className="h-5 w-5 text-emerald-600 mt-0.5" />
                <div>
                  <h4 className="font-medium">Community Building</h4>
                  <p className="text-sm text-gray-600">Connect with neighbors, share local insights, or organize group orders</p>
                </div>
              </div>
            </div>
          </section>

          <Separator />

          {/* Features */}
          <section>
            <h3 className="text-lg font-semibold mb-3">Messaging Features</h3>
            <div className="grid gap-3 sm:grid-cols-2">
              <div className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4 text-emerald-600" />
                <span className="text-sm">Real-time messaging</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-emerald-600" />
                <span className="text-sm">Message history</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-emerald-600" />
                <span className="text-sm">Privacy controls</span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-emerald-600" />
                <span className="text-sm">Group conversations</span>
              </div>
            </div>
          </section>

          <Separator />

          {/* Privacy & Safety */}
          <section>
            <h3 className="text-lg font-semibold mb-3">Privacy & Safety</h3>
            <div className="space-y-2">
              <div className="flex items-start gap-2">
                <Shield className="h-4 w-4 text-emerald-600 mt-0.5" />
                <p className="text-sm text-gray-600">
                  Your personal information is protected. Only share what you're comfortable with.
                </p>
              </div>
              <div className="flex items-start gap-2">
                <Settings className="h-4 w-4 text-emerald-600 mt-0.5" />
                <p className="text-sm text-gray-600">
                  Control who can message you and customize your privacy settings in your account settings.
                </p>
              </div>
            </div>
          </section>

          <Separator />

          {/* Getting Started */}
          <section>
            <h3 className="text-lg font-semibold mb-3">Getting Started</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs">1</Badge>
                <span className="text-sm">Click "New" to start a conversation</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs">2</Badge>
                <span className="text-sm">Choose who you want to message (business, driver, or customer)</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="w-6 h-6 rounded-full p-0 flex items-center justify-center text-xs">3</Badge>
                <span className="text-sm">Start typing your message and hit send!</span>
              </div>
            </div>
          </section>

          {/* Quick Actions Info */}
          <section className="bg-emerald-50 p-4 rounded-lg">
            <h4 className="font-medium text-emerald-800 mb-2">💡 Pro Tip: Quick Actions</h4>
            <p className="text-sm text-emerald-700">
              Look for Quick Actions on your messages page - they suggest relevant conversations based on your recent orders and activity!
            </p>
          </section>
        </div>

        <div className="flex justify-end pt-4">
          <Button onClick={() => setOpen(false)}>
            Got it!
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
