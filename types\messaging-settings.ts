export interface MessagingSettings {
  // Core messaging
  messaging_enabled: boolean
  
  // Discovery & Identity
  public_username?: string
  discoverable_by_username: boolean
  show_real_name: boolean
  
  // Communication preferences
  allow_business_messages: boolean
  allow_driver_messages: boolean
  allow_customer_messages: boolean
  
  // Order-related
  auto_accept_order_conversations: boolean
  allow_order_reviews: boolean
  review_reminders: boolean
  
  // Privacy
  message_history_retention_days: number // 30, 90, 365, forever (-1)
  block_anonymous_messages: boolean
}

export const DEFAULT_MESSAGING_SETTINGS: MessagingSettings = {
  messaging_enabled: true,
  discoverable_by_username: false,
  show_real_name: true,
  allow_business_messages: true,
  allow_driver_messages: true,
  allow_customer_messages: true,
  auto_accept_order_conversations: true,
  allow_order_reviews: true,
  review_reminders: true,
  message_history_retention_days: 365,
  block_anonymous_messages: false
}

export interface MessagingSettingsResponse {
  settings: MessagingSettings
  hasProfile: boolean
}

export interface MessagingSettingsUpdateResponse {
  success: boolean
  profile?: any
  error?: string
}
