"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Star,
  Store,
  Package,
  Truck,
  Calendar,
  Clock,
  ArrowRight,
  Check
} from "lucide-react"
import { cn } from "@/lib/utils"
import { getAuthHeaders } from '@/utils/auth-utils'

interface Order {
  id: number
  order_number: string
  business_id: number
  driver_id?: string
  status: string
  created_at: string
  businesses: {
    name: string
  }
}

interface ReviewSelectionModalProps {
  isOpen: boolean
  onClose: () => void
  reviewType: 'order' | 'business' | 'driver'
  user: any
  onReviewSubmitted: (reviewData: any) => void
  preSelectedOrder?: Order | null // For quick actions that pre-select an order
}

export function ReviewSelectionModal({
  isOpen,
  onClose,
  reviewType,
  user,
  onReviewSubmitted,
  preSelectedOrder = null
}: ReviewSelectionModalProps) {
  const [step, setStep] = useState<'select' | 'review'>('select')
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [recentOrders, setRecentOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(false)
  const [rating, setRating] = useState(0)
  const [comment, setComment] = useState('')
  const [submitting, setSubmitting] = useState(false)

  useEffect(() => {
    if (isOpen) {
      if (preSelectedOrder) {
        // If we have a pre-selected order, skip the selection step
        setSelectedOrder(preSelectedOrder)
        setStep('review')
        setRating(0)
        setComment('')
      } else {
        // Normal flow - fetch orders and show selection
        fetchRecentOrders()
        setStep('select')
        setSelectedOrder(null)
        setRating(0)
        setComment('')
      }
    }
  }, [isOpen, user, preSelectedOrder])

  const fetchRecentOrders = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/messages/context?user_id=${encodeURIComponent(user.id)}`)
      const data = await response.json()
      
      if (data.success && data.context.recent_completed_orders) {
        setRecentOrders(data.context.recent_completed_orders)
      }
    } catch (error) {
      console.error('Error fetching recent orders:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleOrderSelect = (order: Order) => {
    setSelectedOrder(order)
    setStep('review')
  }

  const handleSubmitReview = async () => {
    if (!selectedOrder || rating === 0) return

    // For driver reviews, check if there's a driver assigned
    if (reviewType === 'driver' && !selectedOrder.driver_id) {
      alert('This order does not have a driver assigned, so you cannot leave a driver review.')
      return
    }

    setSubmitting(true)
    try {
      const reviewData = {
        order_id: selectedOrder.id,
        business_id: selectedOrder.business_id,
        driver_id: selectedOrder.driver_id,
        rating,
        comment: comment.trim(),
        review_type: reviewType === 'order' ? 'business' : reviewType // Map 'order' to 'business'
      }

      console.log('Submitting review data:', reviewData)

      // Get authentication headers
      const headers = await getAuthHeaders({
        'Content-Type': 'application/json',
      })

      // Call the review submission API
      const response = await fetch('/api/reviews', {
        method: 'POST',
        headers,
        body: JSON.stringify(reviewData)
      })

      console.log('Response status:', response.status)
      console.log('Response ok:', response.ok)

      if (response.ok) {
        const result = await response.json()
        console.log('Success result:', result)
        onReviewSubmitted({ ...reviewData, result })
        onClose()
      } else {
        const errorData = await response.json()
        console.error('Failed to submit review:', errorData)
        alert(`Failed to submit review: ${errorData.error || 'Unknown error'}`)
      }
    } catch (error) {
      console.error('Error submitting review:', error)
      console.error('Error details:', JSON.stringify(error))
      alert(`Error submitting review: ${error.message || 'Please try again.'}`)
    } finally {
      setSubmitting(false)
    }
  }

  const getReviewTitle = () => {
    switch (reviewType) {
      case 'order': return 'Review Your Order'
      case 'business': return 'Review Business'
      case 'driver': return 'Review Driver'
      default: return 'Leave a Review'
    }
  }

  const getReviewDescription = () => {
    switch (reviewType) {
      case 'order': return 'Select a recent order to review the food quality and business experience'
      case 'business': return 'Select a recent order to review the business service and experience'
      case 'driver': return 'Select a recent order to review the delivery driver service (only orders with assigned drivers)'
      default: return 'Select a recent order to leave your review'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            {reviewType === 'order' && <Package className="h-5 w-5 mr-2 text-blue-600" />}
            {reviewType === 'business' && <Store className="h-5 w-5 mr-2 text-green-600" />}
            {reviewType === 'driver' && <Truck className="h-5 w-5 mr-2 text-orange-600" />}
            {getReviewTitle()}
          </DialogTitle>
          <DialogDescription>
            {getReviewDescription()}
          </DialogDescription>
        </DialogHeader>

        {step === 'select' && (
          <div className="space-y-4">
            {loading ? (
              <div className="space-y-3">
                {[1, 2, 3].map(i => (
                  <div key={i} className="h-20 bg-gray-200 rounded animate-pulse" />
                ))}
              </div>
            ) : recentOrders.length === 0 ? (
              <div className="text-center py-8">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No recent completed orders found</p>
                <p className="text-sm text-gray-400">You need to have completed orders to leave reviews</p>
              </div>
            ) : (
              <div className="space-y-3">
                <p className="text-sm text-gray-600 mb-4">
                  Select an order from the last 7 days:
                  {reviewType === 'driver' && (
                    <span className="block text-xs text-orange-600 mt-1">
                      Only orders with assigned drivers are shown
                    </span>
                  )}
                </p>
                {recentOrders
                  .filter(order => reviewType !== 'driver' || order.driver_id) // Filter for driver reviews
                  .map((order) => (
                  <Card 
                    key={order.id} 
                    className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-blue-200"
                    onClick={() => handleOrderSelect(order)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <Store className="h-5 w-5 text-blue-600" />
                              </div>
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="font-medium text-gray-900 truncate">
                                {order.businesses.name}
                              </p>
                              <p className="text-sm text-gray-500">
                                Order #{order.order_number}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="text-right">
                            <div className="flex items-center text-sm text-gray-500">
                              <Calendar className="h-4 w-4 mr-1" />
                              {formatDate(order.created_at)}
                            </div>
                            <div className="flex items-center text-sm text-gray-500">
                              <Clock className="h-4 w-4 mr-1" />
                              {formatTime(order.created_at)}
                            </div>
                          </div>
                          <div className="flex flex-col items-end space-y-1">
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                              {order.status}
                            </Badge>
                            {reviewType === 'driver' && order.driver_id && (
                              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">
                                Driver assigned
                              </Badge>
                            )}
                          </div>
                          <ArrowRight className="h-5 w-5 text-gray-400" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}

        {step === 'review' && selectedOrder && (
          <div className="space-y-6">
            {/* Selected Order Summary */}
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <Store className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{selectedOrder.businesses.name}</p>
                    <p className="text-sm text-gray-600">Order #{selectedOrder.order_number}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Rating */}
            <div className="space-y-2">
              <Label className="text-base font-medium">Rating</Label>
              <div className="flex items-center space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    onClick={() => setRating(star)}
                    className={cn(
                      "p-1 rounded transition-colors",
                      star <= rating ? "text-yellow-400" : "text-gray-300 hover:text-yellow-200"
                    )}
                  >
                    <Star className="h-8 w-8 fill-current" />
                  </button>
                ))}
                {rating > 0 && (
                  <span className="ml-2 text-sm text-gray-600">
                    {rating} star{rating !== 1 ? 's' : ''}
                  </span>
                )}
              </div>
            </div>

            {/* Comment */}
            <div className="space-y-2">
              <Label htmlFor="comment" className="text-base font-medium">
                Comment (optional)
              </Label>
              <Textarea
                id="comment"
                placeholder={`Share your experience with ${reviewType === 'driver' ? 'the delivery service' : reviewType === 'business' ? 'this business' : 'your order'}...`}
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                rows={4}
              />
            </div>

            {/* Actions */}
            <div className="flex space-x-3 pt-4">
              <Button
                variant="outline"
                onClick={() => setStep('select')}
                className="flex-1"
              >
                Back to Orders
              </Button>
              <Button
                onClick={handleSubmitReview}
                disabled={rating === 0 || submitting}
                className="flex-1"
              >
                {submitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    Submit Review
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
