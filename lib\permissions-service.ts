/**
 * Permissions Service
 * Database-driven permission checking system
 */

import { adminClient } from './supabase-admin'

export type Permission = 
  | 'access_admin_panel'
  | 'manage_businesses'
  | 'manage_users'
  | 'manage_drivers'
  | 'manage_categories'
  | 'manage_platform_settings'
  | 'view_system_logs'
  | 'approve_drivers'
  | 'manage_business'
  | 'manage_orders'
  | 'manage_products'
  | 'manage_staff'
  | 'view_analytics'
  | 'view_orders'
  | 'view_products'
  | 'view_staff'
  | 'deliver_orders'

export type UserRole = 'customer' | 'business_staff' | 'business_manager' | 'admin' | 'super_admin' | 'driver'

/**
 * Check if a user has a specific permission
 */
export async function userHasPermission(userId: number, permission: Permission): Promise<boolean> {
  try {
    // Check role-based permissions
    const { data: rolePermissions, error: roleError } = await adminClient
      .from('role_permissions')
      .select(`
        permission_id,
        permissions!inner(name)
      `)
      .eq('permissions.name', permission)
      .single()

    if (roleError && roleError.code !== 'PGRST116') {
      console.error('Error checking role permissions:', roleError)
      return false
    }

    // Get user's role
    const { data: user, error: userError } = await adminClient
      .from('users')
      .select('role')
      .eq('id', userId)
      .single()

    if (userError) {
      console.error('Error fetching user role:', userError)
      return false
    }

    if (rolePermissions) {
      // Check if user's role has this permission
      const { data: userRolePermission, error: userRoleError } = await adminClient
        .from('role_permissions')
        .select('id')
        .eq('role_name', user.role)
        .eq('permission_id', rolePermissions.permission_id)
        .single()

      if (!userRoleError && userRolePermission) {
        return true
      }
    }

    // Check user-specific permissions
    const { data: userPermissions, error: userPermError } = await adminClient
      .from('user_permissions')
      .select(`
        permissions!inner(name)
      `)
      .eq('user_id', userId)
      .eq('permissions.name', permission)
      .single()

    if (!userPermError && userPermissions) {
      return true
    }

    return false
  } catch (error) {
    console.error('Error checking user permission:', error)
    return false
  }
}

/**
 * Get all permissions for a user
 */
export async function getUserPermissions(userId: number): Promise<Permission[]> {
  try {
    const permissions: Permission[] = []

    // Get user's role
    const { data: user, error: userError } = await adminClient
      .from('users')
      .select('role')
      .eq('id', userId)
      .single()

    if (userError) {
      console.error('Error fetching user role:', userError)
      return []
    }

    // Get role-based permissions
    const { data: rolePermissions, error: roleError } = await adminClient
      .from('role_permissions')
      .select(`
        permissions!inner(name)
      `)
      .eq('role_name', user.role)

    if (!roleError && rolePermissions) {
      permissions.push(...rolePermissions.map(rp => rp.permissions.name as Permission))
    }

    // Get user-specific permissions
    const { data: userPermissions, error: userPermError } = await adminClient
      .from('user_permissions')
      .select(`
        permissions!inner(name)
      `)
      .eq('user_id', userId)

    if (!userPermError && userPermissions) {
      permissions.push(...userPermissions.map(up => up.permissions.name as Permission))
    }

    // Remove duplicates
    return [...new Set(permissions)]
  } catch (error) {
    console.error('Error getting user permissions:', error)
    return []
  }
}

/**
 * Check if a user has admin access (access_admin_panel permission)
 */
export async function userHasAdminAccess(userId: number): Promise<boolean> {
  return await userHasPermission(userId, 'access_admin_panel')
}

/**
 * Check if a user has business admin access
 */
export async function userHasBusinessAdminAccess(userId: number): Promise<boolean> {
  const permissions = await getUserPermissions(userId)
  return permissions.some(p => 
    p === 'manage_business' || 
    p === 'manage_orders' || 
    p === 'view_orders' ||
    p === 'access_admin_panel'
  )
}

/**
 * Check if a user role has admin privileges
 */
export function isAdminRole(role: UserRole): boolean {
  return role === 'admin' || role === 'super_admin'
}

/**
 * Check if a user role has business admin privileges
 */
export function isBusinessAdminRole(role: UserRole): boolean {
  return role === 'business_manager' || role === 'business_staff' || isAdminRole(role)
}
