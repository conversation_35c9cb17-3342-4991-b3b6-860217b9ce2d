import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyBusinessAdminAccess } from '@/lib/simple-auth'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Create a Supabase client with service role key for admin operations
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

// GET - Fetch business attributes
export async function GET(request: NextRequest) {
  try {
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const { user, profile: userProfile } = accessCheck

    // Get business ID from query parameter for admin users, or from business_managers table for business managers
    const { searchParams } = new URL(request.url)
    const queryBusinessId = searchParams.get('businessId')
    let businessId: number | null = null

    if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
      // Admin users can access any business
      if (queryBusinessId) {
        businessId = parseInt(queryBusinessId)
      } else {
        // Default to first business if no businessId specified
        const { data: firstBusiness } = await adminClient
          .from('businesses')
          .select('id')
          .limit(1)
          .single()

        if (firstBusiness) {
          businessId = firstBusiness.id
        }
      }
    } else {
      // Check if user is a business manager
      const { data: managerData, error: managerError } = await adminClient
        .from("business_managers")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .single()

      if (managerData && !managerError) {
        businessId = managerData.business_id
      }
    }

    if (!businessId) {
      return NextResponse.json(
        { error: 'No business associated with this account' },
        { status: 400 }
      )
    }

    // Get business details to include business type
    const { data: business, error: businessError } = await adminClient
      .from('businesses')
      .select('business_type_id')
      .eq('id', businessId)
      .single()

    if (businessError) {
      console.error('Error fetching business:', businessError)
      return NextResponse.json(
        { error: 'Failed to fetch business details' },
        { status: 500 }
      )
    }

    // Get business's current attributes
    const { data: attributes, error: attributesError } = await adminClient
      .from('business_attributes')
      .select('business_id, attribute_type, attribute_value')
      .eq('business_id', businessId)
      .order('attribute_type', { ascending: true })
      .order('attribute_value', { ascending: true })

    if (attributesError) {
      console.error('Error fetching attributes:', attributesError)
      return NextResponse.json(
        { error: 'Failed to fetch attributes' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      attributes: attributes || [],
      business_type_id: business.business_type_id
    })
  } catch (error) {
    console.error('Error in GET /api/business-admin/attributes:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Add attribute
export async function POST(request: NextRequest) {
  try {
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const { user, profile: userProfile } = accessCheck

    // Get business ID from query parameter for admin users, or from business_managers table for business managers
    const { searchParams } = new URL(request.url)
    const queryBusinessId = searchParams.get('businessId')
    let businessId: number | null = null

    if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
      // Admin users can access any business
      if (queryBusinessId) {
        businessId = parseInt(queryBusinessId)
      } else {
        // Default to first business if no businessId specified
        const { data: firstBusiness } = await adminClient
          .from('businesses')
          .select('id')
          .limit(1)
          .single()

        if (firstBusiness) {
          businessId = firstBusiness.id
        }
      }
    } else {
      // Check if user is a business manager
      const { data: managerData, error: managerError } = await adminClient
        .from("business_managers")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .single()

      if (managerData && !managerError) {
        businessId = managerData.business_id
      }
    }

    if (!businessId) {
      return NextResponse.json(
        { error: 'No business associated with this account' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { attribute_type, attribute_value } = body

    if (!attribute_type || !attribute_value) {
      return NextResponse.json(
        { error: 'Attribute type and value are required' },
        { status: 400 }
      )
    }

    // Check if attribute already exists
    const { data: existing, error: existingError } = await adminClient
      .from('business_attributes')
      .select('*')
      .eq('business_id', businessId)
      .eq('attribute_type', attribute_type)
      .eq('attribute_value', attribute_value)
      .single()

    if (existing) {
      return NextResponse.json(
        { error: 'Attribute already exists' },
        { status: 400 }
      )
    }

    // Add the attribute
    const { error: insertError } = await adminClient
      .from('business_attributes')
      .insert({
        business_id: businessId,
        attribute_type,
        attribute_value
      })

    if (insertError) {
      console.error('Error adding attribute:', insertError)
      return NextResponse.json(
        { error: 'Failed to add attribute' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Attribute added successfully'
    })
  } catch (error) {
    console.error('Error in POST /api/business-admin/attributes:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE - Remove attribute
export async function DELETE(request: NextRequest) {
  try {
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const { user, profile: userProfile } = accessCheck

    // Get business ID from query parameter for admin users, or from business_managers table for business managers
    const { searchParams } = new URL(request.url)
    const queryBusinessId = searchParams.get('businessId')
    let businessId: number | null = null

    if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
      // Admin users can access any business
      if (queryBusinessId) {
        businessId = parseInt(queryBusinessId)
      } else {
        // Default to first business if no businessId specified
        const { data: firstBusiness } = await adminClient
          .from('businesses')
          .select('id')
          .limit(1)
          .single()

        if (firstBusiness) {
          businessId = firstBusiness.id
        }
      }
    } else {
      // Check if user is a business manager
      const { data: managerData, error: managerError } = await adminClient
        .from("business_managers")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .single()

      if (managerData && !managerError) {
        businessId = managerData.business_id
      }
    }

    if (!businessId) {
      return NextResponse.json(
        { error: 'No business associated with this account' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { attribute_type, attribute_value } = body

    if (!attribute_type || !attribute_value) {
      return NextResponse.json(
        { error: 'Attribute type and value are required' },
        { status: 400 }
      )
    }

    // Remove the attribute
    const { error: deleteError } = await adminClient
      .from('business_attributes')
      .delete()
      .eq('business_id', businessId)
      .eq('attribute_type', attribute_type)
      .eq('attribute_value', attribute_value)

    if (deleteError) {
      console.error('Error removing attribute:', deleteError)
      return NextResponse.json(
        { error: 'Failed to remove attribute' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Attribute removed successfully'
    })
  } catch (error) {
    console.error('Error in DELETE /api/business-admin/attributes:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
