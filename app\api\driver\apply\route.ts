import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(request: NextRequest) {
  try {
    console.log('Driver application API called')
    const body = await request.json()
    console.log('Request body received:', { ...body, email: body.email ? '[REDACTED]' : undefined })

    const {
      firstName,
      lastName,
      email,
      phone,
      vehicleType,
      licenseNumber,
      insuranceNumber,
      vehicleRegistration,
      notes,
      previousDeliveryExperience,
      availabilityHours,
      preferredAreas,
      userId
    } = body

    // Validate required fields
    if (!firstName || !lastName || !email || !phone || !vehicleType) {
      console.log('Missing required fields:', { firstName: !!firstName, lastName: !!lastName, email: !!email, phone: !!phone, vehicleType: !!vehicleType })
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      )
    }

    let finalUserId = userId

    // If no user ID provided, check if user exists or create new account
    if (!finalUserId) {
      console.log('No user ID provided, checking if user exists for email:', email)

      // First, check if a user already exists with this email
      const { data: existingUser, error: lookupError } = await supabase
        .from('users')
        .select('id, auth_id')
        .eq('email', email.toLowerCase())
        .single()

      if (lookupError && lookupError.code !== 'PGRST116') {
        console.error('Error looking up existing user:', lookupError)
        return NextResponse.json(
          { error: 'Failed to check existing user' },
          { status: 500 }
        )
      }

      if (existingUser) {
        console.log('Found existing user:', existingUser.id)
        // Use the existing user's integer ID for driver_profiles.user_id
        finalUserId = existingUser.id

        // Check if they already have a driver profile
        const { data: existingDriver, error: driverLookupError } = await supabase
          .from('driver_profiles')
          .select('id')
          .eq('user_id', finalUserId)
          .single()

        if (existingDriver) {
          return NextResponse.json(
            { error: 'You already have a driver application on file. Please contact support if you need to update your information.' },
            { status: 400 }
          )
        }

        // Update existing user's information with driver application data
        console.log('Updating existing user information with driver application data')
        const userUpdateData: any = {}

        // Update name fields if provided and different
        if (firstName && lastName) {
          userUpdateData.first_name = firstName
          userUpdateData.last_name = lastName
          userUpdateData.name = `${firstName} ${lastName}`
        }

        // Update phone if provided
        if (phone) {
          userUpdateData.phone = phone
        }

        // Only update if there are changes to make
        if (Object.keys(userUpdateData).length > 0) {
          const { error: updateError } = await supabase
            .from('users')
            .update({
              ...userUpdateData,
              updated_at: new Date().toISOString()
            })
            .eq('id', finalUserId)

          if (updateError) {
            console.error('Error updating existing user:', updateError)
            // Don't fail the application for this, just log it
            console.log('Continuing with driver profile creation despite user update error')
          } else {
            console.log('Successfully updated existing user information')
          }
        }
      } else {
        console.log('Creating new user account for:', email)

        // Create the user in Supabase Auth - the trigger will automatically create the public user
        const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
          email,
          email_confirm: true,
          user_metadata: {
            full_name: `${firstName} ${lastName}`,
            first_name: firstName,
            last_name: lastName,
            phone
          }
        })

        if (authError) {
          console.error('Error creating auth user:', authError)
          return NextResponse.json(
            { error: `Failed to create user account: ${authError.message}` },
            { status: 500 }
          )
        }

        console.log('Auth user created:', authUser.user.id)
        console.log('Public user will be created automatically by trigger')

        // Wait a moment for the trigger to complete
        await new Promise(resolve => setTimeout(resolve, 100))

        // Get the integer ID from the public users table
        const { data: newPublicUser, error: publicUserError } = await supabase
          .from('users')
          .select('id')
          .eq('auth_id', authUser.user.id)
          .single()

        if (publicUserError || !newPublicUser) {
          console.error('Error finding newly created public user:', publicUserError)
          return NextResponse.json(
            { error: 'Failed to find user profile after creation' },
            { status: 500 }
          )
        }

        finalUserId = newPublicUser.id
        console.log('Using public user ID:', finalUserId)
      }
    } else {
      console.log('Using existing user ID:', finalUserId)

      // If userId was provided, it might be a UUID from auth, so we need to get the integer ID
      // First check if it's already an integer
      if (typeof finalUserId === 'string' && finalUserId.includes('-')) {
        // It's a UUID, look up the integer ID
        const { data: existingUser, error: lookupError } = await supabase
          .from('users')
          .select('id')
          .eq('auth_id', finalUserId)
          .single()

        if (lookupError) {
          console.error('Error looking up user by auth_id:', lookupError)
          return NextResponse.json(
            { error: 'Failed to find user profile' },
            { status: 500 }
          )
        }

        if (existingUser) {
          finalUserId = existingUser.id
          console.log('Converted UUID to integer ID:', finalUserId)
        }
      }
    }

    // Generate a UUID for the driver profile using crypto
    const driverProfileId = crypto.randomUUID()
    console.log('Generated driver profile ID:', driverProfileId)

    // Create the driver profile
    const driverProfileData = {
      id: driverProfileId,
      user_id: finalUserId,
      vehicle_type: vehicleType,
      license_number: licenseNumber || null,
      insurance_number: insuranceNumber || null,
      vehicle_registration: vehicleRegistration || null,
      is_active: true,
      is_verified: false, // Will be verified by admin
      // Initialize capacity and equipment fields with defaults
      has_thermal_bag: false,
      has_cooler_bag: false,
      has_large_bag: false,
      delivery_types: [],
      store_types: [],
      notes: [
        notes,
        `Previous experience: ${previousDeliveryExperience || 'None specified'}`,
        `Availability: ${availabilityHours || 'Not specified'}`,
        `Preferred areas: ${preferredAreas || 'Not specified'}`
      ].filter(Boolean).join('\n\n'),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    console.log('Creating driver profile with data:', { ...driverProfileData, user_id: '[REDACTED]' })

    const { data: driverProfile, error: driverError } = await supabase
      .from('driver_profiles')
      .insert(driverProfileData)
      .select()
      .single()

    if (driverError) {
      console.error('Error creating driver profile:', driverError)
      return NextResponse.json(
        { error: `Failed to create driver profile: ${driverError.message}` },
        { status: 500 }
      )
    }

    console.log('Driver profile created successfully:', driverProfile?.id)

    // Log the driver activity
    const { error: activityError } = await supabase
      .from('driver_activity_log')
      .insert({
        driver_id: driverProfileId,
        activity_type: 'application_submitted',
        timestamp: new Date().toISOString(),
        notes: 'Driver application submitted and pending verification'
      })

    if (activityError) {
      console.error('Error logging driver activity:', activityError)
      // Don't fail the request for this, just log it
    }

    // Create admin notification for new driver application
    try {
      const { data: adminUsers } = await supabase
        .from('users')
        .select('id')
        .eq('role', 'admin')

      if (adminUsers && adminUsers.length > 0) {
        const notifications = adminUsers.map(admin => ({
          admin_user_id: admin.id,
          type: 'driver_application',
          title: 'New Driver Application',
          message: `${firstName} ${lastName} has submitted a driver application`,
          action_url: `/admin/driver-applications/${driverProfileId}`,
          priority: 'medium',
          related_table: 'driver_profiles',
          related_record_id: driverProfileId,
          metadata: {
            driver_name: `${firstName} ${lastName}`,
            driver_email: email,
            vehicle_type: vehicleType,
            phone: phone
          }
        }))

        await supabase
          .from('admin_notifications')
          .insert(notifications)

        console.log(`Created admin notifications for ${adminUsers.length} admin users`)
      }
    } catch (notificationError) {
      console.error('Error creating admin notification:', notificationError)
      // Don't fail the request if notification fails
    }

    return NextResponse.json({
      success: true,
      message: "Driver application submitted successfully",
      driverProfileId: driverProfileId,
      userId: finalUserId
    })

  } catch (error) {
    console.error('Error in driver application:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
