import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { verifyBusinessAdminAccess } from "@/lib/simple-auth";

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const adminClient = createClient(supabaseUrl, supabaseServiceKey);

export async function GET(request: Request) {
  try {
    console.log("Business data API called");

    // Use the same auth pattern as super-admin routes
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    // Parse the URL to get query parameters
    const url = new URL(request.url);
    const businessId = url.searchParams.get('businessId');

    console.log("Business data API called with businessId:", businessId);

    const userProfile = accessCheck.profile;

    console.log("User profile:", {
      id: userProfile.id,
      email: userProfile.email,
      role: userProfile.role
    });

    // Check if user is admin or super admin
    const isAdmin = userProfile.role === "admin" || userProfile.role === "super_admin";

    // Determine which business to fetch data for
    let targetBusinessId: number | null = null;

    if (businessId) {
      // Admin user requesting specific business
      if (!isAdmin) {
        return NextResponse.json(
          { error: "Only admins can specify business ID" },
          { status: 403 }
        );
      }
      targetBusinessId = parseInt(businessId);
    } else {
      // Regular user - use their associated business
      if (!isAdmin && !userProfile.business_id) {
        return NextResponse.json(
          { error: "No business associated with this account" },
          { status: 403 }
        );
      }

      // For admin users without a business_id, we need a business ID parameter
      if (isAdmin && !userProfile.business_id) {
        return NextResponse.json(
          { error: "Admin users must specify a business ID parameter" },
          { status: 400 }
        );
      }

      targetBusinessId = userProfile.business_id;
    }

    console.log("Fetching business data for business ID:", targetBusinessId);

    // Fetch business data
    const { data: business, error: businessError } = await adminClient
      .from("businesses")
      .select(`
        id,
        name,
        slug,
        description,
        address,
        phone,
        email,
        website,
        image_url,
        is_active,
        business_type,
        opening_hours,
        delivery_fee,
        minimum_order,
        estimated_delivery_time,
        parish,
        postcode,
        coordinates,
        created_at,
        updated_at
      `)
      .eq("id", targetBusinessId)
      .single();

    if (businessError) {
      console.error("Error fetching business data:", businessError);
      return NextResponse.json(
        { error: "Failed to fetch business data" },
        { status: 500 }
      );
    }

    if (!business) {
      return NextResponse.json(
        { error: "Business not found" },
        { status: 404 }
      );
    }

    console.log("Successfully fetched business data for:", business.name);

    return NextResponse.json({
      business,
      user: {
        id: userProfile.id,
        email: userProfile.email,
        role: userProfile.role,
        isAdmin
      }
    });

  } catch (error) {
    console.error("Business data API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
