/**
 * Admin Configuration
 * Environment-based configuration for admin users and security settings
 */

import { UserRole } from './auth-utils'

// Environment-based admin configuration
export const ADMIN_CONFIG = {
  // Super admin emails (highest privilege)
  SUPER_ADMIN_EMAILS: process.env.SUPER_ADMIN_EMAILS?.split(',').map(email => email.trim()) || [],

  // Platform admin emails
  ADMIN_EMAILS: process.env.ADMIN_EMAILS?.split(',').map(email => email.trim()) || [],

  // Security settings
  RBAC_AUDIT_ENABLED: process.env.RBAC_AUDIT_ENABLED === 'true',
  RBAC_STRICT_MODE: process.env.RBAC_STRICT_MODE === 'true',

  // Session settings
  SESSION_TIMEOUT_MINUTES: parseInt(process.env.SESSION_TIMEOUT_MINUTES || '60'),
  REQUIRE_2FA_FOR_ADMINS: process.env.REQUIRE_2FA_FOR_ADMINS === 'true',

  // Development settings
  ALLOW_DEV_BYPASS: process.env.NODE_ENV === 'development' && process.env.ALLOW_DEV_BYPASS === 'true'
}

/**
 * Check if email is configured as super admin
 */
export function isSuperAdminEmail(email: string): boolean {
  return ADMIN_CONFIG.SUPER_ADMIN_EMAILS.includes(email.toLowerCase())
}

/**
 * Check if email is configured as admin
 */
export function isAdminEmail(email: string): boolean {
  return ADMIN_CONFIG.ADMIN_EMAILS.includes(email.toLowerCase())
}

/**
 * Get default role for email based on configuration
 * This is only used for NEW user registration, not existing users
 */
export function getDefaultRoleForEmail(email: string): UserRole {
  const lowerEmail = email.toLowerCase()

  // Only assign admin roles to new users if they're in environment config
  // Existing users keep their database role regardless of environment config
  if (ADMIN_CONFIG.SUPER_ADMIN_EMAILS.includes(lowerEmail)) {
    return 'super_admin'
  }

  if (ADMIN_CONFIG.ADMIN_EMAILS.includes(lowerEmail)) {
    return 'admin'
  }

  return 'customer'
}

/**
 * Check if user should have emergency admin access
 * This provides a fallback when database roles fail
 * @deprecated Use database permissions instead of hardcoded emails
 */
export function hasEmergencyAdminAccess(email: string): boolean {
  // Deprecated: Use database permissions instead
  return false
}

/**
 * Validate admin configuration on startup
 */
export function validateAdminConfig(): {
  valid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []

  // Check if super admin emails are configured
  if (ADMIN_CONFIG.SUPER_ADMIN_EMAILS.length === 0) {
    warnings.push('No super admin emails configured. Use SUPER_ADMIN_EMAILS environment variable.')
  }

  // Validate email formats
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

  ADMIN_CONFIG.SUPER_ADMIN_EMAILS.forEach(email => {
    if (!emailRegex.test(email)) {
      errors.push(`Invalid super admin email format: ${email}`)
    }
  })

  ADMIN_CONFIG.ADMIN_EMAILS.forEach(email => {
    if (!emailRegex.test(email)) {
      errors.push(`Invalid admin email format: ${email}`)
    }
  })

  // Check for overlapping emails
  const overlap = ADMIN_CONFIG.SUPER_ADMIN_EMAILS.filter(email =>
    ADMIN_CONFIG.ADMIN_EMAILS.includes(email)
  )

  if (overlap.length > 0) {
    warnings.push(`Emails configured as both super admin and admin: ${overlap.join(', ')}`)
  }

  // Security warnings
  if (ADMIN_CONFIG.ALLOW_DEV_BYPASS && process.env.NODE_ENV === 'production') {
    errors.push('Development bypass is enabled in production environment')
  }

  if (!ADMIN_CONFIG.RBAC_STRICT_MODE) {
    warnings.push('RBAC strict mode is disabled')
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Log admin configuration status (without exposing sensitive data)
 */
export function logAdminConfigStatus(): void {
  const validation = validateAdminConfig()

  console.log('Admin Configuration Status:', {
    superAdminCount: ADMIN_CONFIG.SUPER_ADMIN_EMAILS.length,
    adminCount: ADMIN_CONFIG.ADMIN_EMAILS.length,
    auditEnabled: ADMIN_CONFIG.RBAC_AUDIT_ENABLED,
    strictMode: ADMIN_CONFIG.RBAC_STRICT_MODE,
    sessionTimeout: ADMIN_CONFIG.SESSION_TIMEOUT_MINUTES,
    require2FA: ADMIN_CONFIG.REQUIRE_2FA_FOR_ADMINS,
    devBypass: ADMIN_CONFIG.ALLOW_DEV_BYPASS,
    valid: validation.valid,
    errorCount: validation.errors.length,
    warningCount: validation.warnings.length
  })

  if (validation.errors.length > 0) {
    console.error('Admin Configuration Errors:', validation.errors)
  }

  if (validation.warnings.length > 0) {
    console.warn('Admin Configuration Warnings:', validation.warnings)
  }
}

/**
 * Initialize admin configuration
 * Call this on application startup
 */
export function initializeAdminConfig(): boolean {
  try {
    logAdminConfigStatus()
    const validation = validateAdminConfig()

    if (!validation.valid) {
      console.error('Admin configuration validation failed. Application may not function correctly.')
      return false
    }

    console.log('Admin configuration initialized successfully')
    return true
  } catch (error) {
    console.error('Failed to initialize admin configuration:', error)
    return false
  }
}

// Example .env.local configuration:
/*
# Super Admin Configuration
SUPER_ADMIN_EMAILS=<EMAIL>,<EMAIL>

# Platform Admin Configuration
ADMIN_EMAILS=<EMAIL>,<EMAIL>

# Security Settings
RBAC_AUDIT_ENABLED=true
RBAC_STRICT_MODE=true
SESSION_TIMEOUT_MINUTES=60
REQUIRE_2FA_FOR_ADMINS=false

# Development Settings (only for development)
ALLOW_DEV_BYPASS=false
*/
